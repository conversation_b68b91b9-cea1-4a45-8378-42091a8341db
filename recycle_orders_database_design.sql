-- ========================================
-- 回收订单数据库设计（基于现有系统优化）
-- 分析现有数据库：已有估价订单、会员地址、加价券等表
-- 设计回收订单来处理实际回收流程
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 回收订单主表
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_orders`;
CREATE TABLE `www_yz_she_recycle_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应www_member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',

  -- 订单状态和流程
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',

  -- 价格信息
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应www_yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',

  -- 配送信息
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应www_member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',

  -- 快递信息
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',

  -- 质检信息
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',

  -- 结算信息（直接结算到用户余额）
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',

  -- 备注信息
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',

  -- 时间记录（对应状态流转）
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',

  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no` (`order_no`) USING BTREE,
  INDEX `idx_quote_order_id` (`quote_order_id`) USING BTREE,
  INDEX `idx_member_id` (`member_id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE,
  INDEX `idx_source_type` (`source_type`) USING BTREE,
  INDEX `idx_delivery_type` (`delivery_type`) USING BTREE,
  INDEX `idx_create_time` (`create_time`) USING BTREE,
  INDEX `idx_member_status_time` (`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id` (`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id` (`voucher_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 2. 回收订单状态变更日志表（参考现有估价订单日志表结构）
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs`;
CREATE TABLE `www_yz_she_recycle_order_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',

  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id` (`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id` (`operator_id`) USING BTREE,
  INDEX `idx_create_time` (`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 注意：根据要求删除回收订单配件表和照片表
-- 原因：
-- 1. 直接回收不涉及照片和配件
-- 2. 如果是从估价订单创建的回收订单，照片和配件信息直接使用现有的估价订单相关表：
--    - www_yz_she_quote_photos（估价订单照片表）
--    - www_yz_she_quote_accessories（估价订单配件表）
-- 3. 质检照片可以存储在主表的 quality_images 字段中（JSON格式）
-- ----------------------------

-- ----------------------------
-- 注意：用户地址表已存在 www_member_address，无需重复创建
-- 现有表结构完全满足需求，包含：
-- - member_id: 会员ID
-- - name: 联系人姓名
-- - mobile: 联系人电话
-- - province_id, city_id, district_id: 省市区
-- - address: 详细地址
-- - full_address: 完整地址
-- - lng, lat: 经纬度
-- - is_default: 是否默认地址
-- ----------------------------

-- ----------------------------
-- 注意：根据要求删除快递公司配置表
-- 快递公司信息可以在前端硬编码或使用简单的配置
-- ----------------------------

-- ----------------------------
-- 注意：根据要求删除评价数据表
-- 第一版系统暂不实现评价功能
-- ----------------------------

-- ----------------------------
-- 5. 触发器：自动记录状态变更日志（参考现有估价订单触发器）
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_recycle_orders_status_log`;
DELIMITER ;;
CREATE TRIGGER `tr_recycle_orders_status_log` AFTER UPDATE ON `www_yz_she_recycle_orders` FOR EACH ROW
BEGIN
  -- 当状态发生变化时，自动记录日志
  IF OLD.status != NEW.status THEN
    INSERT INTO `www_yz_she_recycle_order_logs` (
      `recycle_order_id`,
      `from_status`,
      `to_status`,
      `operator_type`,
      `change_reason`,
      `create_time`
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      3, -- 系统操作
      CONCAT('状态从 ', OLD.status, ' 变更为 ', NEW.status),
      UNIX_TIMESTAMP()
    );
  END IF;
END;;
DELIMITER ;

-- ----------------------------
-- 6. 结算处理存储过程（可选实现）
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_settle_recycle_order`;
DELIMITER ;;
CREATE PROCEDURE `sp_settle_recycle_order`(
  IN p_order_id INT,
  IN p_admin_id INT,
  OUT p_result INT,
  OUT p_message VARCHAR(255)
)
BEGIN
  DECLARE v_member_id INT;
  DECLARE v_settlement_amount DECIMAL(10,2);
  DECLARE v_order_status TINYINT;
  DECLARE v_settlement_status TINYINT;
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    SET p_result = 0;
    SET p_message = '结算处理失败';
  END;

  START TRANSACTION;

  -- 获取订单信息
  SELECT member_id, total_amount, status, settlement_status
  INTO v_member_id, v_settlement_amount, v_order_status, v_settlement_status
  FROM www_yz_she_recycle_orders
  WHERE id = p_order_id;

  -- 检查订单状态
  IF v_order_status != 7 THEN
    SET p_result = 0;
    SET p_message = '订单状态不正确，无法结算';
    ROLLBACK;
  ELSEIF v_settlement_status = 1 THEN
    SET p_result = 0;
    SET p_message = '订单已结算，请勿重复操作';
    ROLLBACK;
  ELSE
    -- 更新用户余额
    UPDATE www_member
    SET money = money + v_settlement_amount
    WHERE member_id = v_member_id;

    -- 记录账单流水
    INSERT INTO www_member_account_log (
      member_id, account_type, account_data, account_sum,
      from_type, related_id, memo, create_time
    ) VALUES (
      v_member_id, 'money', v_settlement_amount,
      (SELECT money FROM www_member WHERE member_id = v_member_id),
      'recycle_settlement', p_order_id,
      CONCAT('回收订单结算，订单号：', (SELECT order_no FROM www_yz_she_recycle_orders WHERE id = p_order_id)),
      UNIX_TIMESTAMP()
    );

    -- 更新订单结算状态
    UPDATE www_yz_she_recycle_orders
    SET settlement_status = 1,
        settlement_amount = v_settlement_amount,
        settlement_admin_id = p_admin_id,
        settlement_time = UNIX_TIMESTAMP()
    WHERE id = p_order_id;

    COMMIT;
    SET p_result = 1;
    SET p_message = '结算成功';
  END IF;
END;;
DELIMITER ;

-- ----------------------------
-- 注意：用户地址默认状态管理
-- 由于使用现有的 www_member_address 表，
-- 地址默认状态的触发器应该在该表上设置，
-- 这里不重复创建
-- ----------------------------

-- ----------------------------
-- 7. 视图：回收订单统计视图（适配时间戳格式）
-- ----------------------------
DROP VIEW IF EXISTS `v_recycle_order_stats`;
CREATE VIEW `v_recycle_order_stats` AS
SELECT
  DATE(FROM_UNIXTIME(create_time)) as order_date,
  COUNT(*) as total_orders,
  COUNT(CASE WHEN status = 1 THEN 1 END) as pickup_pending_orders,    -- 待取件
  COUNT(CASE WHEN status = 2 THEN 1 END) as receive_pending_orders,   -- 待收货
  COUNT(CASE WHEN status = 3 THEN 1 END) as quality_pending_orders,   -- 待质检
  COUNT(CASE WHEN status = 4 THEN 1 END) as confirm_pending_orders,   -- 待确认
  COUNT(CASE WHEN status = 5 THEN 1 END) as return_pending_orders,    -- 待退回
  COUNT(CASE WHEN status = 6 THEN 1 END) as returned_orders,          -- 已退回
  COUNT(CASE WHEN status = 7 THEN 1 END) as completed_orders,         -- 已完成
  COUNT(CASE WHEN source_type = 1 THEN 1 END) as from_quote_orders,
  COUNT(CASE WHEN source_type = 2 THEN 1 END) as direct_orders,
  COUNT(CASE WHEN delivery_type = 1 THEN 1 END) as pickup_orders,
  COUNT(CASE WHEN delivery_type = 2 THEN 1 END) as self_ship_orders,
  ROUND(AVG(CASE WHEN status = 7 THEN total_amount END), 2) as avg_amount,
  SUM(CASE WHEN status = 7 THEN total_amount ELSE 0 END) as total_amount
FROM `www_yz_she_recycle_orders`
GROUP BY DATE(FROM_UNIXTIME(create_time))
ORDER BY order_date DESC;

-- ----------------------------
-- 8. 添加必要的外键约束（可选，根据实际需要）
-- ----------------------------
-- ALTER TABLE `www_yz_she_recycle_orders`
-- ADD CONSTRAINT `fk_recycle_quote_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT;

-- ALTER TABLE `www_yz_she_recycle_orders`
-- ADD CONSTRAINT `fk_recycle_member` FOREIGN KEY (`member_id`) REFERENCES `www_member` (`member_id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ALTER TABLE `www_yz_she_recycle_orders`
-- ADD CONSTRAINT `fk_recycle_address` FOREIGN KEY (`pickup_address_id`) REFERENCES `www_member_address` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT;

-- ALTER TABLE `www_yz_she_recycle_orders`
-- ADD CONSTRAINT `fk_recycle_voucher` FOREIGN KEY (`voucher_id`) REFERENCES `www_yz_she_voucher` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT;

-- ----------------------------
-- 9. 订单状态流转说明
-- ----------------------------
/*
订单状态流转图：

1. 待取件 → 2. 待收货 → 3. 待质检 → 4. 待确认 → 7. 已完成
                                      ↓
                                   5. 待退回 → 6. 已退回

状态说明：
1 = 待取件：订单已创建，等待快递员上门取件或用户自行寄出
2 = 待收货：商品已发出，等待平台收货确认
3 = 待质检：平台已收到商品，等待质检员进行质检
4 = 待确认：质检完成，等待用户确认是否接受质检价格
5 = 待退回：用户不接受质检价格，商品准备退回
6 = 已退回：商品已退回给用户
7 = 已完成：用户接受质检价格，交易完成并结算

关键时间节点：
- pickup_time_actual: 实际取件时间（1→2）
- receive_time: 平台收货时间（2→3）
- quality_start_time: 质检开始时间（3→4）
- quality_complete_time: 质检完成时间（4状态完成）
- confirm_time: 用户确认时间（4→7或4→5）
- return_time: 商品退回时间（5→6）
- settlement_time: 结算完成时间（7状态完成）

结算流程说明：
1. 用户确认接受质检价格后，订单状态变为"已完成"(7)
2. 系统自动将结算金额(settlement_amount = final_price + voucher_amount)加入用户余额
3. 同时在会员账单表(www_member_account_log)中记录余额变动
4. 用户可在个人中心查看余额，并通过提现功能提取到微信/支付宝/银行卡

数据关联：
- 结算金额直接加入 www_member.money 字段
- 在 www_member_account_log 表记录账单流水
- account_type = 'money', from_type = 'recycle_settlement', related_id = recycle_order_id
*/

SET FOREIGN_KEY_CHECKS = 1;
