/*
 Navicat Premium Dump SQL

 Source Server         : other_likeapi_cn
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : other_likeapi_cn

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 29/07/2025 13:48:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for www_account_log
-- ----------------------------
DROP TABLE IF EXISTS `www_account_log`;
CREATE TABLE `www_account_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pay' COMMENT '账单类型pay,refund,transfer',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应类型交易单号',
  `create_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '站点账单记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_addon
-- ----------------------------
DROP TABLE IF EXISTS `www_addon`;
CREATE TABLE `www_addon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件图标',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '插件描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态',
  `author` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '作者',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `install_time` int(11) NOT NULL DEFAULT 0 COMMENT '安装时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'app' COMMENT '插件类型app，addon',
  `support_app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件支持的应用空表示通用插件',
  `is_star` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否加星',
  `compile` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编译端口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '插件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_addon_log
-- ----------------------------
DROP TABLE IF EXISTS `www_addon_log`;
CREATE TABLE `www_addon_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `action` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型   install 安装 uninstall 卸载 update 更新',
  `key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `from_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级前的版本号',
  `to_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级后的版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '插件日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_applet_site_version
-- ----------------------------
DROP TABLE IF EXISTS `www_applet_site_version`;
CREATE TABLE `www_applet_site_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `version_id` int(11) NOT NULL DEFAULT 0 COMMENT '版本id',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序类型',
  `action` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作方式 download 下载  upgrade 更新',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '站点小程序版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_applet_version
-- ----------------------------
DROP TABLE IF EXISTS `www_applet_version`;
CREATE TABLE `www_applet_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置信息',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序类型',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '插件描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态  下架  上架',
  `uid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布者',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序包地址',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `version_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号数字(用于排序)',
  `release_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布线上版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_form
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form`;
CREATE TABLE `www_diy_form`  (
  `form_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表单id',
  `page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称（用于后台展示）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称（用于前台展示）',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单类型',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0，关闭，1：开启）',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单数据，json格式，包含展示组件',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件标识',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `write_num` int(11) NOT NULL DEFAULT 0 COMMENT '表单填写总数量',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注说明',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`form_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_fields
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_fields`;
CREATE TABLE `www_diy_form_fields`  (
  `field_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '字段id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `field_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段唯一标识',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段说明',
  `field_default` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字段默认值',
  `write_num` int(11) NOT NULL DEFAULT 0 COMMENT '字段填写总数量',
  `field_required` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否必填 0:否 1:是',
  `field_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否隐藏 0:否 1:是',
  `field_unique` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段内容防重复 0:否 1:是',
  `privacy_protection` tinyint(4) NOT NULL DEFAULT 0 COMMENT '隐私保护 0:关闭 1:开启',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`field_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_records
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_records`;
CREATE TABLE `www_diy_form_records`  (
  `record_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表单填写记录id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '填写的表单数据',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '填写人会员id',
  `relate_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联业务id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_records_fields
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_records_fields`;
CREATE TABLE `www_diy_form_records_fields`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `form_field_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联表单字段id',
  `record_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联表单填写记录id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '填写会员id',
  `field_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段唯一标识',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段值，根据类型展示对应效果',
  `field_required` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否必填 0:否 1:是',
  `field_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否隐藏 0:否 1:是',
  `field_unique` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段内容防重复 0:否 1:是',
  `privacy_protection` tinyint(4) NOT NULL DEFAULT 0 COMMENT '隐私保护 0:关闭 1:开启',
  `update_num` int(11) NOT NULL DEFAULT 0 COMMENT '字段修改次数',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_submit_config
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_submit_config`;
CREATE TABLE `www_diy_form_submit_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `submit_after_action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '填表人提交后操作，text：文字信息，voucher：核销凭证',
  `tips_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提示内容类型，default：默认提示，diy：自定义提示',
  `tips_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义提示内容',
  `time_limit_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '核销凭证有效期限制类型，no_limit：不限制，specify_time：指定固定开始结束时间，submission_time：按提交时间设置有效期',
  `time_limit_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '核销凭证时间限制规则，json格式',
  `voucher_content_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '核销凭证内容，json格式',
  `success_after_action` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '填写成功后续操作',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单提交页配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_write_config
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_write_config`;
CREATE TABLE `www_diy_form_write_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `write_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '填写方式，no_limit：不限制，scan：仅限微信扫一扫，url：仅限链接进入',
  `join_member_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_member' COMMENT '参与会员，all_member：所有会员参与，selected_member_level：指定会员等级，selected_member_label：指定会员标签',
  `level_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员等级id集合',
  `label_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员标签id集合',
  `member_write_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '每人可填写次数，no_limit：不限制，diy：自定义',
  `member_write_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '每人可填写次数自定义规则',
  `form_write_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单可填写数量，no_limit：不限制，diy：自定义',
  `form_write_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单可填写总数自定义规则',
  `time_limit_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '填写时间限制类型，no_limit：不限制， specify_time：指定开始结束时间，open_day_time：设置每日开启时间',
  `time_limit_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '填写时间限制规则',
  `is_allow_update_content` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否允许修改自己填写的内容，0：否，1：是',
  `write_instruction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单填写须知',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_page
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_page`;
CREATE TABLE `www_diy_page`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面名称（用于后台展示）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标题（用于前台展示）',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标识',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面模板',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'diy' COMMENT '页面展示模式，diy：自定义，fixed：固定',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '页面数据，json格式',
  `is_default` int(11) NOT NULL DEFAULT 0 COMMENT '是否默认页面，1：是，0：否',
  `is_change` int(11) NOT NULL DEFAULT 0 COMMENT '数据是否发生过变化，1：变化了，2：没有',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `visit_count` int(11) NOT NULL DEFAULT 0 COMMENT '访问量',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义页面' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_route
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_route`;
CREATE TABLE `www_diy_route`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面名称',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标识',
  `page` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面路径',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `is_share` int(11) NOT NULL DEFAULT 0 COMMENT '是否支持分享',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义路由' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_theme
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_theme`;
CREATE TABLE `www_diy_theme`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件类型app，addon',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属应用，app：系统，shop：商城、o2o：上门服务',
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模式，default：默认【跟随系统】，diy：自定义配色',
  `theme_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配色类型，default：默认，diy：自定义',
  `default_theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前色调的默认值',
  `theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前色调',
  `new_theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新增颜色集合',
  `is_selected` tinyint(4) NOT NULL DEFAULT 0 COMMENT '已选色调，0：否，1.是',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义主题配色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_generate_column
-- ----------------------------
DROP TABLE IF EXISTS `www_generate_column`;
CREATE TABLE `www_generate_column`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_id` int(11) NOT NULL DEFAULT 0 COMMENT '表id',
  `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `column_comment` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `is_required` tinyint(1) NULL DEFAULT 0 COMMENT '是否必填 0-非必填 1-必填',
  `is_pk` tinyint(1) NULL DEFAULT 0 COMMENT '是否为主键 0-不是 1-是',
  `is_insert` tinyint(1) NULL DEFAULT 0 COMMENT '是否为插入字段 0-不是 1-是',
  `is_update` tinyint(1) NULL DEFAULT 0 COMMENT '是否为更新字段 0-不是 1-是',
  `is_lists` tinyint(1) NULL DEFAULT 1 COMMENT '是否为列表字段 0-不是 1-是',
  `is_query` tinyint(1) NULL DEFAULT 1 COMMENT '是否为查询字段 0-不是 1-是',
  `is_search` tinyint(1) NULL DEFAULT 1 COMMENT '是否搜索字段',
  `query_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '=' COMMENT '查询类型',
  `view_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'input' COMMENT '显示类型',
  `dict_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉关联应用',
  `model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉关联model',
  `label_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉标题字段',
  `value_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉value字段',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `is_delete` tinyint(4) NULL DEFAULT 0 COMMENT '是否为软删除字段 0-不是 1-是',
  `is_order` tinyint(4) NULL DEFAULT 0 COMMENT '是否为排序字段 0-不是 1-是',
  `validate_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表字段信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_generate_table
-- ----------------------------
DROP TABLE IF EXISTS `www_generate_table`;
CREATE TABLE `www_generate_table`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表名',
  `table_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述前缀',
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块名',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类名前缀',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `edit_type` int(11) NOT NULL DEFAULT 1 COMMENT '编辑方式 1-弹框 2-新页面',
  `addon_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件名',
  `order_type` int(11) NOT NULL DEFAULT 0 COMMENT '排序方式 0-无排序 1-正序 2-倒序',
  `parent_menu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上级菜单',
  `relations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联配置',
  `synchronous_number` int(11) NOT NULL DEFAULT 0 COMMENT '同步次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_jobs
-- ----------------------------
DROP TABLE IF EXISTS `www_jobs`;
CREATE TABLE `www_jobs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `attempts` tinyint(4) UNSIGNED NOT NULL DEFAULT 0,
  `reserve_time` int(11) UNSIGNED NULL DEFAULT 0,
  `available_time` int(11) UNSIGNED NULL DEFAULT 0,
  `create_time` int(11) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息队列任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_jobs_failed
-- ----------------------------
DROP TABLE IF EXISTS `www_jobs_failed`;
CREATE TABLE `www_jobs_failed`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fail_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息队列任务失败记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member
-- ----------------------------
DROP TABLE IF EXISTS `www_member`;
CREATE TABLE `www_member`  (
  `member_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `member_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员编码',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '推广会员id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员用户名',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员密码',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员昵称',
  `headimg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员头像',
  `member_level` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `member_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员标签',
  `wx_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信用户openid',
  `weapp_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信小程序openid',
  `wx_unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信unionid',
  `ali_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账户id',
  `douyin_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '抖音小程序openid',
  `register_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'H5' COMMENT '注册来源',
  `register_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册方式',
  `login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前登录ip',
  `login_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'h5' COMMENT '当前登录的操作终端类型',
  `login_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `login_time` int(11) NOT NULL DEFAULT 0 COMMENT '当前登录时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '注册时间',
  `last_visit_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后访问时间',
  `last_consum_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后消费时间',
  `sex` tinyint(4) NOT NULL DEFAULT 0 COMMENT '性别 0保密 1男 2女',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '用户状态  用户状态默认为1',
  `birthday` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '出生日期',
  `id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '可用积分',
  `point_get` int(11) NOT NULL DEFAULT 0 COMMENT '累计获取积分',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额',
  `balance_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计获取余额',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额（可提现）',
  `money_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计获取余额（可提现）',
  `money_cash_outing` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现中余额（可提现）',
  `growth` int(11) NOT NULL DEFAULT 0 COMMENT '成长值',
  `growth_get` int(11) NOT NULL DEFAULT 0 COMMENT '累计获得成长值',
  `commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '当前佣金',
  `commission_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金获取',
  `commission_cash_outing` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现中佣金',
  `is_member` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是会员',
  `member_time` int(11) NOT NULL DEFAULT 0 COMMENT '成为会员时间',
  `is_del` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0正常  1已删除',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '定位地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_account_log
-- ----------------------------
DROP TABLE IF EXISTS `www_member_account_log`;
CREATE TABLE `www_member_account_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'point' COMMENT '账户类型',
  `account_data` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '账户数据',
  `account_sum` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动后的账户余额',
  `from_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源类型',
  `related_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联Id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_address
-- ----------------------------
DROP TABLE IF EXISTS `www_member_address`;
CREATE TABLE `www_member_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户姓名',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址信息',
  `address_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `full_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址信息',
  `lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `is_default` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是默认地址',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_member_address`(`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员收货地址' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_cash_out
-- ----------------------------
DROP TABLE IF EXISTS `www_member_cash_out`;
CREATE TABLE `www_member_cash_out`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cash_out_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现交易号',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'money' COMMENT '提现账户类型',
  `transfer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '转账提现类型',
  `transfer_realname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `transfer_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `transfer_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `transfer_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款账号',
  `transfer_payee` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账收款方(json),主要用于对接在线的打款方式',
  `transfer_payment_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码图片',
  `transfer_fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `transfer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账状态',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT '转账时间',
  `apply_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现申请金额',
  `rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费比率',
  `service_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现到账金额',
  `audit_time` int(11) NOT NULL DEFAULT 0 COMMENT '审核时间',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态1待审核2.待转账3已转账 -1拒绝 -2 已取消',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `refuse_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `transfer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账单号',
  `cancel_time` int(11) NOT NULL DEFAULT 0 COMMENT '取消时间',
  `final_transfer_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账方式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_cash_out_account
-- ----------------------------
DROP TABLE IF EXISTS `www_member_cash_out_account`;
CREATE TABLE `www_member_cash_out_account`  (
  `account_id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账户类型',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `realname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '真实名称',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `account_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现账户',
  `transfer_payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码',
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员提现账户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_label
-- ----------------------------
DROP TABLE IF EXISTS `www_member_label`;
CREATE TABLE `www_member_label`  (
  `label_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `label_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `memo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`label_id`) USING BTREE,
  INDEX `label_id`(`label_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_level
-- ----------------------------
DROP TABLE IF EXISTS `www_member_level`;
CREATE TABLE `www_member_level`  (
  `level_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '会员等级',
  `level_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级名称',
  `growth` int(11) NOT NULL DEFAULT 0 COMMENT '所需成长值',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态 0已禁用1已启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `level_benefits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级权益',
  `level_gifts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级礼包',
  PRIMARY KEY (`level_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员等级' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_sign
-- ----------------------------
DROP TABLE IF EXISTS `www_member_sign`;
CREATE TABLE `www_member_sign`  (
  `sign_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `days` int(11) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `day_award` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '日签奖励',
  `continue_award` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '连签奖励',
  `continue_tag` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '连签奖励标识',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '签到时间',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '签到周期开始时间',
  `is_sign` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否签到（0未签到 1已签到）',
  PRIMARY KEY (`sign_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员签到表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_niu_sms_template
-- ----------------------------
DROP TABLE IF EXISTS `www_niu_sms_template`;
CREATE TABLE `www_niu_sms_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信服务商类型 niuyun-牛云 aliyun-阿里云 tencent-腾讯',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '子账号名称',
  `template_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版key',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版id',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版类型',
  `template_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版内容',
  `param_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数变量',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上下架状态',
  `audit_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '报备、审核状态',
  `audit_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核结果/拒绝原因',
  `report_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报备、审核信息',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '牛云短信模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_pay
-- ----------------------------
DROP TABLE IF EXISTS `www_pay`;
CREATE TABLE `www_pay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '支付会员id',
  `from_main_id` int(11) NOT NULL DEFAULT 0 COMMENT '发起支付会员id',
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水号',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `trade_id` int(11) NOT NULL DEFAULT 0 COMMENT '业务id',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `body` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付主体',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付票据',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '支付状态（0.待支付 1. 支付中 2. 已支付 -1已取消）',
  `json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付扩展用支付信息',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '支付时间',
  `cancel_time` int(11) NOT NULL DEFAULT 0 COMMENT '关闭时间',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `mch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户收款账号',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_channel
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_channel`;
CREATE TABLE `www_pay_channel`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付类型',
  `channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付配置',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_refund
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_refund`;
CREATE TABLE `www_pay_refund`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号',
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水号',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款原因',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '支付状态（0.待退款 1. 退款中 2. 已退款 -1已关闭）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `refund_time` int(11) NOT NULL DEFAULT 0 COMMENT '支付时间',
  `close_time` int(11) NOT NULL DEFAULT 0 COMMENT '关闭时间',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付凭证',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `trade_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务关联id',
  `refund_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款方式',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人类型',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `pay_refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部支付方式的退款单号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付退款记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_transfer
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_transfer`;
CREATE TABLE `www_pay_transfer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `transfer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账单号',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主体类型',
  `transfer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账类型',
  `transfer_realname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `transfer_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `transfer_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `transfer_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款账号',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '凭证',
  `transfer_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '凭证说明',
  `transfer_payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码图片',
  `transfer_fail_reason` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `transfer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账状态',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '转账金额',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT '转账时间',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `batch_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账批次id',
  `transfer_payee` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '在线转账数据(json)',
  `out_batch_no` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展数据,主要用于记录接收到线上打款的业务数据编号',
  `package_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转领取页面的package信息',
  `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '转账表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_transfer_scene
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_transfer_scene`;
CREATE TABLE `www_pay_transfer_scene`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `scene` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '场景',
  `infos` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账报备背景',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `perception` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账收款感知',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付转账场景表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_shop_active
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_active`;
CREATE TABLE `www_shop_active`  (
  `active_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '活动id',
  `active_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `active_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动说明',
  `active_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动类型(店铺活动，会员活动，商品活动)',
  `active_goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品活动类型（单品，独立商品，店铺整体商品）',
  `active_goods_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参与活动商品信息',
  `active_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动类别',
  `active_class_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动类别子分类（活动管理）',
  `relate_member` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参与会员条件(默认全部)',
  `active_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动扩展信息数据',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `active_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `active_order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计金额',
  `active_order_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计订单数',
  `active_member_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动参与会员数',
  `active_success_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动成功参与会员数',
  PRIMARY KEY (`active_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺营销活动表（整体活动）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_active_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_active_goods`;
CREATE TABLE `www_shop_active_goods`  (
  `active_goods_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '活动商品id',
  `active_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_id` int(11) NULL DEFAULT 0 COMMENT '商品规格id',
  `active_goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品活动类型（单品，独立商品，店铺整体商品）',
  `active_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品活动类别',
  `active_goods_label` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动商品标签（针对活动有标签）',
  `active_goods_category` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动商品分类（针对活动有分类）',
  `active_goods_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动商品信息数据',
  `active_goods_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动状态',
  `active_goods_point` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动商品积分（展示，搜索）',
  `active_goods_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动商品价格（展示，搜索）',
  `active_goods_stock` int(11) NOT NULL DEFAULT 0 COMMENT '活动商品库存（针对参与库存）',
  `active_goods_order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计金额',
  `active_goods_order_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计订单数',
  `active_goods_member_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动参与会员数',
  `active_goods_success_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动成功参与会员数',
  PRIMARY KEY (`active_goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺营销活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_address
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_address`;
CREATE TABLE `www_shop_address`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contact_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `full_address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `lat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `lng` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `is_delivery_address` int(11) NOT NULL DEFAULT 0 COMMENT '是否是发货地址',
  `is_refund_address` int(11) NOT NULL DEFAULT 0 COMMENT '是否是退货地址',
  `is_default_delivery` int(11) NOT NULL DEFAULT 0 COMMENT '默认发货地址',
  `is_default_refund` int(11) NOT NULL DEFAULT 0 COMMENT '默认收货地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家地址库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_cart
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_cart`;
CREATE TABLE `www_shop_cart`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '购物车表ID',
  `member_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `goods_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `sku_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'sku id',
  `num` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品数量',
  `market_type` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动类型',
  `market_type_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动id',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '购物车商品状态',
  `invalid_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失效原因',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `sku_id`(`sku_id`) USING BTREE,
  INDEX `type`(`market_type`) USING BTREE,
  INDEX `type_id`(`market_type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_coupon
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_coupon`;
CREATE TABLE `www_shop_coupon`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动开启时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `remain_count` int(11) NOT NULL DEFAULT 0 COMMENT '剩余数量',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已领取数量',
  `give_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `limit_count` int(11) NOT NULL DEFAULT 0 COMMENT '单个会员限制领取数量',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 状态 1 正常 2 未开启 3 已无效',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '面值',
  `min_condition_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品最低多少金额可用优惠券',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '优惠券类型 1通用优惠券 2商品品类优惠券 3商品优惠券',
  `receive_type` int(11) NOT NULL DEFAULT 0 COMMENT '领取方式',
  `valid_type` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效时间',
  `length` int(11) NOT NULL DEFAULT 0 COMMENT '有效期时长(天)',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `receive_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 状态 1 正常 2 关闭',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `title`(`title`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_coupon_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_coupon_goods`;
CREATE TABLE `www_shop_coupon_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券模板id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_category_id`(`category_id`) USING BTREE,
  INDEX `index_coupon_id`(`coupon_id`) USING BTREE,
  INDEX `index_goods_id`(`goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券商品或品类关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_coupon_member
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_coupon_member`;
CREATE TABLE `www_shop_coupon_member`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券发放记录id',
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '领取时间',
  `expire_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '过期时间',
  `use_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用时间',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券类型',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠券名称',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '面值',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用门槛',
  `receive_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '领取方式',
  `trade_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联业务id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `coupon_id`(`coupon_id`) USING BTREE,
  INDEX `member_id`(`member_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券会员领取记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_coupon_send_records
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_coupon_send_records`;
CREATE TABLE `www_shop_coupon_send_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `coupon_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `send_num` int(11) NOT NULL COMMENT '每位会员发放数量',
  `range_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发券范围',
  `range_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '范围对应参数',
  `success_num` int(11) NOT NULL DEFAULT 0 COMMENT '发放成功数',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态 wait-待发送 process-发送中 finish-结束',
  `member_num` int(11) NOT NULL DEFAULT 0 COMMENT '发放会员数',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放结束时间',
  `admin_uid` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `admin_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人名称',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券发券记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_company
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_company`;
CREATE TABLE `www_shop_delivery_company`  (
  `company_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司logo',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司网站',
  `express_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司编号(用于物流跟踪)',
  `express_no_electronic_sheet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司编号(用于电子面单)',
  `electronic_sheet_switch` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否支持电子面单（0：不支持，1：支持）',
  `print_style` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单打印模板样式，json字符串',
  `exp_type` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司业务类型，json字符串',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `update_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`company_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '站点快递表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_deliver
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_deliver`;
CREATE TABLE `www_shop_delivery_deliver`  (
  `deliver_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配送员id',
  `deliver_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送员名称',
  `deliver_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送员手机号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modify_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  PRIMARY KEY (`deliver_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配送员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_electronic_sheet
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_electronic_sheet`;
CREATE TABLE `www_shop_delivery_electronic_sheet`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `express_company_id` int(11) NOT NULL DEFAULT 0 COMMENT '物流公司id',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单客户账号（CustomerName）',
  `customer_pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单密码（CustomerPwd）',
  `send_site` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SendSite',
  `send_staff` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SendStaff',
  `month_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'MonthCode',
  `pay_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '邮费支付方式（1：现付，2：到付，3：月结）',
  `is_notice` tinyint(4) NOT NULL DEFAULT 0 COMMENT '快递员上门揽件（0：否，1：是）',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（1：开启，0：关闭）',
  `exp_type` int(11) NOT NULL DEFAULT 0 COMMENT '物流公司业务类型',
  `print_style` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电子面单打印模板样式',
  `is_default` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否默认（1：是，0：否）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电子面单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_local_delivery
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_local_delivery`;
CREATE TABLE `www_shop_delivery_local_delivery`  (
  `local_id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '费用类型',
  `base_dist` decimal(10, 1) NOT NULL DEFAULT 0.0 COMMENT '多少km内',
  `base_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '配送费用',
  `grad_dist` decimal(10, 1) NOT NULL DEFAULT 0.0 COMMENT '每超出多少km内',
  `grad_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '配送费用',
  `weight_start` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '重量多少内不额外收费',
  `weight_unit` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '每超出多少kg',
  `weight_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `delivery_type` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送类型',
  `area` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送区域',
  `center` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发货地址中心点',
  `time_is_open` tinyint(4) NOT NULL DEFAULT 0 COMMENT '配送时间设置 0 关闭 1 开启',
  `time_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '时间选取类型 0 每天  1 自定义',
  `time_week` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '营业时间  周一 周二.......',
  `time_interval` int(11) NOT NULL DEFAULT 30 COMMENT '时段设置单位分钟',
  `advance_day` int(11) NOT NULL DEFAULT 0 COMMENT '时间选择需提前多少天',
  `most_day` int(11) NOT NULL DEFAULT 7 COMMENT '最多可预约多少天',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '当日的起始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '当日的营业结束时间',
  `delivery_time` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送时间段',
  PRIMARY KEY (`local_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自提点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_shipping_template
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_shipping_template`;
CREATE TABLE `www_shop_delivery_shipping_template`  (
  `template_id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `fee_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '运费计算方式1.重量2体积3按件',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `is_free_shipping` smallint(6) NOT NULL DEFAULT 0 COMMENT '该区域是否包邮',
  `no_delivery` smallint(6) NOT NULL DEFAULT 0 COMMENT '是否指定该区域不配送',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_delivery_shipping_template_item
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_delivery_shipping_template_item`;
CREATE TABLE `www_shop_delivery_shipping_template_item`  (
  `item_id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市id',
  `snum` int(11) NOT NULL DEFAULT 0 COMMENT '起步计算标准',
  `sprice` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '起步计算价格',
  `xnum` int(11) NOT NULL DEFAULT 0 COMMENT '续步计算标准',
  `xprice` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '续步计算价格',
  `fee_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '运费计算方式',
  `fee_area_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运费设置区域id集',
  `fee_area_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运费设置区域名称集',
  `no_delivery` smallint(6) NOT NULL DEFAULT 0 COMMENT '是否指定该区域不配送',
  `no_delivery_area_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '不配送的区域id集',
  `no_delivery_area_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '不配送的区域名称集',
  `is_free_shipping` smallint(6) NOT NULL DEFAULT 0 COMMENT '该区域是否包邮',
  `free_shipping_area_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包邮的区域id集',
  `free_shipping_area_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '包邮的区域名称集',
  `free_shipping_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '满足包邮的条件',
  `free_shipping_num` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `express_template_item_city_id`(`city_id`) USING BTREE,
  INDEX `express_template_item_fee_type`(`fee_type`) USING BTREE,
  INDEX `express_template_item_template_id`(`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板细节' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_discount
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_discount`;
CREATE TABLE `www_shop_discount`  (
  `discount_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '活动id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动说明',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '活动结束时间',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动状态',
  `order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计金额',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计订单数',
  `member_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动参与会员数',
  `success_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动成功参与会员数',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`discount_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时折扣表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_discount_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_discount_goods`;
CREATE TABLE `www_shop_discount_goods`  (
  `discount_goods_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '活动商品id',
  `discount_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品规格id',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品状态',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '折扣类型',
  `rate` decimal(10, 1) NOT NULL COMMENT '折扣',
  `reduce_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '减钱',
  `discount_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动商品价格（展示，搜索）',
  `order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计金额',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计订单数',
  `member_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动参与会员数',
  `success_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动成功参与会员数',
  `is_enabled` int(11) NOT NULL DEFAULT 1 COMMENT '是否参与活动',
  PRIMARY KEY (`discount_goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 134 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '限时折扣商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods`;
CREATE TABLE `www_shop_goods`  (
  `goods_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商品id',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'real' COMMENT '商品类型',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '副标题',
  `goods_cover` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `goods_image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品图片',
  `goods_video` varchar(555) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商品视频',
  `goods_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品分类',
  `goods_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品介绍',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品品牌id',
  `label_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签组',
  `service_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品服务',
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '件' COMMENT '单位',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '商品库存（总和）',
  `sale_num` int(11) NOT NULL DEFAULT 0 COMMENT '销量',
  `virtual_sale_num` int(11) NOT NULL DEFAULT 0 COMMENT '虚拟销量',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '商品状态（1.正常0下架）',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `delivery_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支持的配送方式',
  `is_free_shipping` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否免邮',
  `fee_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '运费设置，选择模板：template，固定运费：fixed',
  `delivery_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '固定运费',
  `delivery_template_id` int(11) NOT NULL DEFAULT 0 COMMENT '运费模板',
  `virtual_auto_delivery` tinyint(4) NOT NULL DEFAULT 0 COMMENT '虚拟商品是否自动发货',
  `virtual_receive_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'artificial' COMMENT '虚拟商品收货方式，auto：自动收货，artificial：买家确认收货，verify：到店核销',
  `virtual_verify_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '虚拟商品核销有效期类型，0：不限，1：购买后几日有效，2：指定过期日期',
  `virtual_indate` int(11) NOT NULL DEFAULT 0 COMMENT '虚拟到期时间',
  `supplier_id` int(11) NOT NULL DEFAULT 0 COMMENT '供应商id',
  `attr_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品参数id',
  `attr_format` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品参数内容，json格式',
  `is_discount` int(11) NOT NULL DEFAULT 0 COMMENT '是否参与限时折扣',
  `member_discount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员等级折扣，不参与：空，会员折扣：discount，指定会员价：fixed_price',
  `poster_id` int(11) NOT NULL DEFAULT 0 COMMENT '海报id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '万能表单id',
  `is_limit` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品是否限购(0:否 1:是)',
  `limit_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '限购类型，1：单次限购，2：单人限购',
  `max_buy` int(11) NOT NULL DEFAULT 0 COMMENT '限购数',
  `min_buy` int(11) NOT NULL DEFAULT 0 COMMENT '起购数',
  `is_gift` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商品是否赠品(0:否 1:是)',
  `access_num` int(11) NOT NULL DEFAULT 0 COMMENT '访问次数（浏览量）',
  `cart_num` int(11) NOT NULL DEFAULT 0 COMMENT '加入购物车数量',
  `pay_num` int(11) NOT NULL DEFAULT 0 COMMENT '支付件数',
  `pay_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付总金额',
  `collect_num` int(11) NOT NULL DEFAULT 0 COMMENT '收藏数量',
  `evaluate_num` int(11) NOT NULL DEFAULT 0 COMMENT '评论数量',
  `refund_num` int(11) NOT NULL DEFAULT 0 COMMENT '退款件数',
  `refund_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款总额',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`goods_id`) USING BTREE,
  INDEX `idx_goods_category`(`goods_category`) USING BTREE,
  INDEX `idx_goods_create_time`(`create_time`) USING BTREE,
  INDEX `idx_goods_delete_time`(`delete_time`) USING BTREE,
  INDEX `idx_goods_name`(`goods_name`) USING BTREE,
  INDEX `idx_goods_sort`(`sort`) USING BTREE,
  INDEX `idx_goods_status`(`status`) USING BTREE,
  INDEX `idx_goods_sub_title`(`sub_title`) USING BTREE,
  INDEX `IDX_ns_goods_goods_class`(`goods_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_attr
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_attr`;
CREATE TABLE `www_shop_goods_attr`  (
  `attr_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品参数id',
  `attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数名称',
  `attr_value_format` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数值，json格式',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '参数排序号',
  PRIMARY KEY (`attr_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品参数表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_brand
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_brand`;
CREATE TABLE `www_shop_goods_brand`  (
  `brand_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `brand_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌logo',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌介绍',
  `color_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义颜色（文字、背景、边框），json格式',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`brand_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_browse
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_browse`;
CREATE TABLE `www_shop_goods_browse`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '浏览人',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT 'sku_id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `browse_time` int(11) NOT NULL DEFAULT 0 COMMENT '浏览时间',
  `goods_cover` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品浏览历史' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_category
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_category`;
CREATE TABLE `www_shop_goods_category`  (
  `category_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品分类id',
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类图片',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '层级',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级分类id',
  `category_full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组装分类名称',
  `is_show` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否显示（1：显示，0：不显示）',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_collect
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_collect`;
CREATE TABLE `www_shop_goods_collect`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_member_collect_goods`(`goods_id`) USING BTREE,
  INDEX `IDX_member_collect_member`(`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品收藏记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_evaluate`;
CREATE TABLE `www_shop_goods_evaluate`  (
  `evaluate_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `order_goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单项ID',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
  `member_head` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员头像',
  `member_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员名称',
  `content` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评价内容',
  `images` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评价图片',
  `is_anonymous` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1匿名  2不匿名',
  `scores` tinyint(4) NOT NULL DEFAULT 1 COMMENT '评论分数 1-5',
  `is_audit` tinyint(4) NOT NULL DEFAULT 1 COMMENT '审核状态 1待审 2通过 3拒绝',
  `explain_first` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '解释内容',
  `topping` int(11) NOT NULL DEFAULT 0 COMMENT '排序 置顶',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '评论时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`evaluate_id`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_create_time`(`create_time`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_is_anonymous`(`is_anonymous`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_is_audit`(`is_audit`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_member_id`(`member_id`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_order_id`(`order_id`) USING BTREE,
  INDEX `idx_shop_goods_evaluate_scores`(`scores`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_label
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_label`;
CREATE TABLE `www_shop_goods_label`  (
  `label_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `label_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `group_id` int(11) NOT NULL DEFAULT 0 COMMENT '标签分组id',
  `style_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '效果设置，diy：自定义，icon：图片',
  `color_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义颜色（文字、背景、边框），json格式',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态，1：启用，0；关闭',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签说明',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`label_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_label_group
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_label_group`;
CREATE TABLE `www_shop_goods_label_group`  (
  `group_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品标签分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_rank
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_rank`;
CREATE TABLE `www_shop_goods_rank`  (
  `rank_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '榜单名称',
  `rank_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '排行周期 day=天，week=周，month=月, quarter=季度',
  `goods_source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源类型 goods=指定商品，category=指定分类，brand=指定品牌, label=指定标签',
  `rule_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '排序规则 sale=按照销量，collect=按收藏数，evaluate=按评价数, access=按照浏览量',
  `goods_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品信息',
  `category_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品分类id',
  `brand_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '商品品牌id',
  `label_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品标签id，多个逗号隔开',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '显示状态（0不显示 1显示）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`rank_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品排行榜' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_service
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_service`;
CREATE TABLE `www_shop_goods_service`  (
  `service_id` int(11) NOT NULL AUTO_INCREMENT,
  `service_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`service_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品服务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_sku`;
CREATE TABLE `www_shop_goods_sku`  (
  `sku_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商品sku_id',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品sku名称',
  `sku_image` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'sku主图',
  `sku_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品sku编码',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_spec_format` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'sku规格格式',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'sku单价',
  `market_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '划线价',
  `sale_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际卖价（有活动显示活动价，默认原价）',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'sku成本价',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '商品sku库存',
  `weight` decimal(10, 3) NOT NULL DEFAULT 0.000 COMMENT '重量（单位kg）',
  `volume` decimal(10, 3) NOT NULL DEFAULT 0.000 COMMENT '体积（单位立方米）',
  `sale_num` int(11) NOT NULL DEFAULT 0 COMMENT '销量',
  `is_default` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否默认',
  `member_price` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员价，json格式，指定会员价，数据结构为：{\"level_1\":\"10.00\",\"level_2\":\"10.00\"}',
  PRIMARY KEY (`sku_id`) USING BTREE,
  INDEX `idx_goods_sku_is_default`(`is_default`) USING BTREE,
  INDEX `idx_goods_sku_price`(`price`) USING BTREE,
  INDEX `idx_goods_sku_sale_price`(`sale_price`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2351 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_spec
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_spec`;
CREATE TABLE `www_shop_goods_spec`  (
  `spec_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规格id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联商品id',
  `spec_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规格项名称',
  `spec_values` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规格值名称，多个逗号隔开',
  PRIMARY KEY (`spec_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格项/值表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_goods_stat
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_goods_stat`;
CREATE TABLE `www_shop_goods_stat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '日期',
  `date_time` int(11) NOT NULL DEFAULT 0 COMMENT '时间戳',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `cart_num` int(11) NOT NULL DEFAULT 0 COMMENT '加入购物车数量',
  `sale_num` int(11) NOT NULL DEFAULT 0 COMMENT '商品销量（下单数）',
  `pay_num` int(11) NOT NULL DEFAULT 0 COMMENT '支付件数',
  `pay_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付总金额',
  `refund_num` int(11) NOT NULL DEFAULT 0 COMMENT '退款件数',
  `refund_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款总额',
  `access_num` int(11) NOT NULL DEFAULT 0 COMMENT '访问次数（浏览量）',
  `collect_num` int(11) NOT NULL DEFAULT 0 COMMENT '收藏数量',
  `evaluate_num` int(11) NOT NULL DEFAULT 0 COMMENT '评论数量',
  `goods_visit_member_count` int(11) NOT NULL DEFAULT 0 COMMENT '商品访客数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品数据统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_inbound_order
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_inbound_order`;
CREATE TABLE `www_shop_inbound_order`  (
  `inbound_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 入库单 ID',
  `site_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 站点 ID',
  `inbound_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 入库单号 ',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 仓库 ID',
  `inbound_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 入库类型：1 - 采购入库，2 - 退货入库，3 - 调拨入库，4 - 其他入库 ',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 供应商名称 ',
  `supplier_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 供应商联系人 ',
  `supplier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 供应商电话 ',
  `total_goods_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品总数量 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 状态：1 - 待入库，2 - 已入库，3 - 已取消 ',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT ' 备注 ',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 操作人 ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 操作人姓名 ',
  `inbound_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 入库时间 ',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 创建时间 ',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 更新时间 ',
  PRIMARY KEY (`inbound_id`) USING BTREE,
  UNIQUE INDEX `uk_inbound_no`(`inbound_no`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  INDEX `idx_warehouse_id`(`warehouse_id`) USING BTREE,
  INDEX `idx_inbound_type`(`inbound_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 入库单主表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_inbound_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_inbound_order_detail`;
CREATE TABLE `www_shop_inbound_order_detail`  (
  `detail_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 详情 ID',
  `inbound_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 入库单 ID',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品 ID',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT 'SKU ID',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 商品名称 ',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 名称 ',
  `sku_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 图片 ',
  `sku_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 编码 ',
  `inbound_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 入库数量 ',
  `unit_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 单位成本 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 备注 ',
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `idx_inbound_id`(`inbound_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_sku_id`(`sku_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 入库单详情表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_invoice
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_invoice`;
CREATE TABLE `www_shop_invoice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '发票id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `trade_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'order' COMMENT '开票分类 order:订单',
  `trade_id` int(11) NOT NULL DEFAULT 0 COMMENT '业务id',
  `header_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '抬头类型',
  `header_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称（发票抬头）',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发票类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发票内容',
  `tax_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公司税号',
  `mobile` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开票人邮箱',
  `telephone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册地址',
  `bank_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开户银行',
  `bank_card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行账号',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '开票金额',
  `is_invoice` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否开票',
  `invoice_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发票代码',
  `invoice_voucher` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发票凭证',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `invoice_time` int(11) NOT NULL DEFAULT 0 COMMENT '开票时间',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '是否生效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发票表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_manjian
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_manjian`;
CREATE TABLE `www_shop_manjian`  (
  `manjian_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '满减活动id',
  `manjian_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `condition_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'over_n_yuan' COMMENT '条件类型 over_n_yuan:满N元  over_n_piece:满N件',
  `goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_goods' COMMENT '参与商品 all_goods:全部商品参与  selected_goods:指定商品 selected_goods_not:指定商品不参与 ',
  `join_member_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_member' COMMENT '参与会员 all_member:所有会员参与  selected_member_level:指定会员等级  selected_member_label:指定会员标签 ',
  `rule_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ladder' COMMENT '优惠规格 ladder:阶梯优惠  cycle:循环优惠',
  `rule_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '优惠规则json',
  `goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品id集',
  `level_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员等级id集',
  `label_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员标签id集',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0未开始1进行中2已结束-1已关闭）',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT 0 COMMENT '结束时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `total_order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计金额',
  `total_order_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计订单数',
  `total_member_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动参与会员数',
  `total_point` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计赠送积分',
  `total_balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '活动累计赠送余额',
  `total_coupon_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计赠送优惠券数',
  `total_goods_num` int(11) NOT NULL DEFAULT 0 COMMENT '活动累计赠送商品数',
  PRIMARY KEY (`manjian_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满减活动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_manjian_give_records
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_manjian_give_records`;
CREATE TABLE `www_shop_manjian_give_records`  (
  `record_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '赠送记录id',
  `manjian_id` int(11) NOT NULL DEFAULT 0 COMMENT '满减送活动id',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '优惠层级',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '赠送积分数量',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '赠送余额',
  `coupon_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '赠送优惠券',
  `goods_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '赠送商品',
  `sku_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '满足条件的商品规格id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满减送记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_manjian_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_manjian_goods`;
CREATE TABLE `www_shop_manjian_goods`  (
  `manjian_goods_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '满减商品活动id',
  `manjian_id` int(11) NOT NULL DEFAULT 0 COMMENT '满减活动id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格id',
  `goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_goods' COMMENT '参与商品 all_goods:全部商品参与  selected_goods:指定商品 selected_goods_not:指定商品不参与 ',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0未开始1进行中2已结束-1已关闭）',
  PRIMARY KEY (`manjian_goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满减商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_newcomer_member_records
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_newcomer_member_records`;
CREATE TABLE `www_shop_newcomer_member_records`  (
  `record_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `validity_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '参与时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `is_join` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否参与',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '参与订单id',
  `goods_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参与商品id集合',
  `sku_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参与商品规格id集合',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '新人专享会员参与记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order`;
CREATE TABLE `www_shop_order`  (
  `order_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `body` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单内容',
  `order_type` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单类型',
  `order_from` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单来源',
  `out_trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水号',
  `status` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单状态',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ip',
  `goods_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品金额',
  `delivery_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '配送金额',
  `discount_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `pay_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `invoice_id` int(11) NOT NULL DEFAULT 0 COMMENT '发票id，0表示不开发票',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单支付时间',
  `delivery_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单发货时间/自提订单自提时间',
  `buyer_ask_delivery_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '购买人要求的配送/发货/自提时间（文本）',
  `take_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单收货时间',
  `finish_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单完成时间',
  `close_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单关闭时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '是否删除(针对后台)',
  `timeout` int(11) NOT NULL DEFAULT 0 COMMENT '通用业务超时时间记录',
  `delivery_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送方式',
  `take_store_id` int(11) NOT NULL DEFAULT 0 COMMENT '自提点',
  `taker_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `taker_mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人手机号',
  `taker_province` int(11) NOT NULL DEFAULT 0 COMMENT '收货省',
  `taker_city` int(11) NOT NULL DEFAULT 0 COMMENT '收货市',
  `taker_district` int(11) NOT NULL DEFAULT 0 COMMENT '收货区县',
  `taker_address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货地址',
  `taker_full_address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货详细地址',
  `taker_longitude` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货地址经度',
  `taker_latitude` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货详细纬度',
  `taker_store_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货门店',
  `is_enable_refund` int(11) NOT NULL DEFAULT 0 COMMENT '是否允许退款',
  `member_remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员留言信息',
  `shop_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家留言',
  `close_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关闭原因',
  `close_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关闭来源(未支付自动关闭   手动关闭  退款关闭)',
  `refund_status` int(11) NOT NULL DEFAULT 1 COMMENT '退款状态  1不存在退款  2 部分退款  3 全部退款',
  `has_goods_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '包含的商品类型 json',
  `is_evaluate` int(11) NOT NULL DEFAULT 0 COMMENT '是否评论',
  `relate_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联id',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '积分兑换',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '营销类型',
  `form_record_id` int(11) NOT NULL DEFAULT 0 COMMENT '万能表单记录id',
  PRIMARY KEY (`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_batch_delivery
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_batch_delivery`;
CREATE TABLE `www_shop_order_batch_delivery`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态 进行中  已完成  已失败',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型 批量发货  批量打单 ....',
  `total_num` int(11) NOT NULL DEFAULT 0 COMMENT '总发货单数',
  `success_num` int(11) NOT NULL DEFAULT 0 COMMENT '成功发货单数',
  `fail_num` int(11) NOT NULL DEFAULT 0 COMMENT '失败发货单数',
  `data` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '导入文件的路径',
  `output` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对外输出记录',
  `fail_output` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败记录',
  `fail_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单批量发货表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_delivery
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_delivery`;
CREATE TABLE `www_shop_order_delivery`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` int(11) NOT NULL DEFAULT 0,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '包裹名称',
  `delivery_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送方式',
  `sub_delivery_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细配送方式',
  `express_company_id` int(11) NOT NULL DEFAULT 0 COMMENT '快递公司id',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送单号',
  `local_deliver_id` int(11) NOT NULL DEFAULT 0 COMMENT '同城配送员',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '配送状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单发货表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_discount
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_discount`;
CREATE TABLE `www_shop_order_discount`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `order_goods_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参与的订单商品项',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型 discount 优惠，gift 赠送',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '使用数量',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `discount_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '优惠类型',
  `discount_type_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠类型id',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单优惠说明',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单优惠表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_discount_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_discount_goods`;
CREATE TABLE `www_shop_order_discount_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_discount_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单优惠id',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `order_goods_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参与的订单商品项',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型 discount 优惠，gift 赠送',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '使用数量',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单项优惠表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_goods`;
CREATE TABLE `www_shop_order_goods`  (
  `order_goods_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '购买会员id',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品规格id',
  `goods_name` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `sku_name` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品规格名称',
  `goods_image` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `sku_image` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'sku规格图片',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品单价',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '购买数量',
  `goods_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品总价',
  `is_enable_refund` int(11) NOT NULL DEFAULT 0 COMMENT '是否允许退款',
  `goods_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品类型',
  `delivery_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配送状态',
  `delivery_id` int(11) NOT NULL DEFAULT 0 COMMENT '发货单号',
  `discount_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
  `order_refund_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号',
  `order_goods_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单项实付金额',
  `original_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品原价',
  `extend` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数据项扩展',
  `verify_count` int(11) NOT NULL DEFAULT 0 COMMENT '已核销次数',
  `verify_expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间 0 为永久',
  `is_verify` int(11) NOT NULL DEFAULT 0 COMMENT '是否需要核销',
  `shop_active_refund` tinyint(4) NOT NULL DEFAULT 0 COMMENT '商家主动退款（0否  1是）',
  `shop_active_refund_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商家主动退款金额',
  `is_gift` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是赠品（0否  1是）',
  `form_record_id` int(11) NOT NULL DEFAULT 0 COMMENT '万能表单记录id',
  PRIMARY KEY (`order_goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单项表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_log
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_log`;
CREATE TABLE `www_shop_order_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '操作人类型',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `status` int(11) NULL DEFAULT NULL COMMENT '订单状态',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_refund`;
CREATE TABLE `www_shop_order_refund`  (
  `refund_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单id',
  `order_goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单项id',
  `order_refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '退款单号',
  `refund_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '退款方式 ',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '退款原因 ',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `apply_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '申请退款',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际退款',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '退款状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT '转账时间',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '描述' COMMENT '描述',
  `voucher` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '凭证' COMMENT '凭证',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源 system 系统 member 会员',
  `timeout` int(11) NOT NULL DEFAULT 0 COMMENT '操作超时时间',
  `refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款交易号',
  `delivery` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退货配送信息',
  `shop_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上架拒绝原因',
  `refund_address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商家退货地址',
  `is_refund_delivery` int(11) NOT NULL DEFAULT 0 COMMENT '是否退运费',
  PRIMARY KEY (`refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_order_refund_log
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_order_refund_log`;
CREATE TABLE `www_shop_order_refund_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_refund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款编号',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '操作人类型',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id',
  `status` int(11) NULL DEFAULT NULL COMMENT '退款状态',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_outbound_order
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_outbound_order`;
CREATE TABLE `www_shop_outbound_order`  (
  `outbound_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 出库单 ID',
  `site_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 站点 ID',
  `outbound_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 出库单号 ',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 仓库 ID',
  `outbound_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 出库类型：1 - 销售出库，2 - 调拨出库，3 - 报损出库，4 - 其他出库 ',
  `related_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 关联单号（订单号等）',
  `customer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 客户名称 ',
  `customer_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 客户联系人 ',
  `customer_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 客户电话 ',
  `total_goods_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品总数量 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 状态：1 - 待出库，2 - 已出库，3 - 已取消 ',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT ' 备注 ',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 操作人 ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 操作人姓名 ',
  `outbound_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 出库时间 ',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 创建时间 ',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 更新时间 ',
  PRIMARY KEY (`outbound_id`) USING BTREE,
  UNIQUE INDEX `uk_outbound_no`(`outbound_no`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  INDEX `idx_warehouse_id`(`warehouse_id`) USING BTREE,
  INDEX `idx_outbound_type`(`outbound_type`) USING BTREE,
  INDEX `idx_related_no`(`related_no`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 出库单主表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_outbound_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_outbound_order_detail`;
CREATE TABLE `www_shop_outbound_order_detail`  (
  `detail_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 详情 ID',
  `outbound_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 出库单 ID',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品 ID',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT 'SKU ID',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 商品名称 ',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 名称 ',
  `sku_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 图片 ',
  `sku_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 编码 ',
  `outbound_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 出库数量 ',
  `unit_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 单位成本 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 备注 ',
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `idx_outbound_id`(`outbound_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_sku_id`(`sku_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 出库单详情表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_point_exchange
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_point_exchange`;
CREATE TABLE `www_shop_point_exchange`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '兑换活动主键id',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '兑换类型（商品、优惠券、红包）',
  `names` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '兑换标题',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '副标题',
  `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '兑换状态 0 下架  1上架  -1 删除',
  `product_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '兑换产品信息',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '兑换所需积分',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '兑换所需金额',
  `limit_num` int(11) NOT NULL DEFAULT 0 COMMENT '限制数量',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品介绍',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `total_point_num` int(11) NULL DEFAULT 0 COMMENT '积分消费总额',
  `total_price_num` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总支付金额',
  `total_order_num` int(11) NULL DEFAULT 0 COMMENT '订单笔数',
  `total_member_num` int(11) NULL DEFAULT 0 COMMENT '参与会员数',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `stock` int(11) NOT NULL DEFAULT 0 COMMENT '库存',
  `total_exchange_num` int(11) NOT NULL DEFAULT 0 COMMENT '兑换数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分兑换表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_point_exchange_order
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_point_exchange_order`;
CREATE TABLE `www_shop_point_exchange_order`  (
  `order_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '兑换记录id',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水表',
  `exchange_id` int(11) NOT NULL DEFAULT 0 COMMENT '兑换活动id',
  `exchange_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '兑换商品名称',
  `exchange_image` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '兑换商品图片',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '兑换类型',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '消费会员id',
  `member_address_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员地址id',
  `relate_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联业务id',
  `relate_order_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联订单id',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '使用积分',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '赠送余额',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '兑换时间',
  `close_time` int(11) NOT NULL DEFAULT 0 COMMENT '关闭时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '订单删除',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '兑换数量',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单状态',
  `order_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  PRIMARY KEY (`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分兑换订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_stat
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_stat`;
CREATE TABLE `www_shop_stat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '日期',
  `date_time` int(11) NOT NULL DEFAULT 0 COMMENT '时间戳',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '订单总数',
  `sale_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '销售总额',
  `refund_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款总额',
  `access_sum` int(11) NOT NULL DEFAULT 0 COMMENT '访问数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_store
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_store`;
CREATE TABLE `www_shop_store`  (
  `store_id` int(11) NOT NULL AUTO_INCREMENT,
  `store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店名称',
  `store_desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `store_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '门店logo',
  `store_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '县（区）',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `full_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '完整地址',
  `longitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `latitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `trade_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '营业时间(文本展示使用)',
  `time_week` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义的营业时间[\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\"]周日-周六',
  `trade_time_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '营业时间',
  `time_interval` int(11) NOT NULL DEFAULT 0 COMMENT '时段设置（分钟）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`store_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自提门店表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_transfer_order
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_transfer_order`;
CREATE TABLE `www_shop_transfer_order`  (
  `transfer_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 调拨单 ID',
  `site_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 站点 ID',
  `transfer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 调拨单号 ',
  `from_warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 调出仓库 ID',
  `to_warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 调入仓库 ID',
  `total_goods_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品总数量 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT ' 状态：1 - 待调拨，2 - 已调拨，3 - 已取消 ',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT ' 备注 ',
  `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 操作人 ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 操作人姓名 ',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 调拨时间 ',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 创建时间 ',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT ' 更新时间 ',
  PRIMARY KEY (`transfer_id`) USING BTREE,
  UNIQUE INDEX `uk_transfer_no`(`transfer_no`) USING BTREE,
  INDEX `idx_site_id`(`site_id`) USING BTREE,
  INDEX `idx_from_warehouse_id`(`from_warehouse_id`) USING BTREE,
  INDEX `idx_to_warehouse_id`(`to_warehouse_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 调拨单主表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_shop_transfer_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `www_shop_transfer_order_detail`;
CREATE TABLE `www_shop_transfer_order_detail`  (
  `detail_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT ' 详情 ID',
  `transfer_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 调拨单 ID',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT ' 商品 ID',
  `sku_id` int(11) NOT NULL DEFAULT 0 COMMENT 'SKU ID',
  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 商品名称 ',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 名称 ',
  `sku_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 图片 ',
  `sku_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'SKU 编码 ',
  `transfer_num` int(11) NOT NULL DEFAULT 0 COMMENT ' 调拨数量 ',
  `unit_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 单位成本 ',
  `total_cost` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT ' 总成本 ',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 备注 ',
  PRIMARY KEY (`detail_id`) USING BTREE,
  INDEX `idx_transfer_id`(`transfer_id`) USING BTREE,
  INDEX `idx_goods_id`(`goods_id`) USING BTREE,
  INDEX `idx_sku_id`(`sku_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 调拨单详情表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_stat_hour
-- ----------------------------
DROP TABLE IF EXISTS `www_stat_hour`;
CREATE TABLE `www_stat_hour`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件',
  `field` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '统计字段',
  `field_total` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总计',
  `year` int(11) NOT NULL DEFAULT 0 COMMENT '年',
  `month` int(11) NOT NULL DEFAULT 0 COMMENT '月',
  `day` int(11) NOT NULL DEFAULT 0 COMMENT '天',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '当日开始时间戳',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后执行时间',
  `hour_0` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_1` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_2` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_3` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_4` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_5` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_6` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_7` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_8` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_9` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_10` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_11` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_12` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_13` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_14` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_15` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_16` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_17` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_18` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_19` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_20` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_21` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_22` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `hour_23` decimal(10, 2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小时统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_agreement
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_agreement`;
CREATE TABLE `www_sys_agreement`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agreement_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议关键字',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_area
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_area`;
CREATE TABLE `www_sys_area`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `shortname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简称',
  `longitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `latitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `level` smallint(6) NOT NULL DEFAULT 0 COMMENT '级别',
  `sort` mediumint(9) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态1有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 460400501 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_attachment
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_attachment`;
CREATE TABLE `www_sys_attachment`  (
  `att_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始文件名',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '完整地址',
  `dir` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件路径',
  `att_size` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件大小',
  `att_type` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件类型image,video',
  `storage_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片上传类型 local本地  aliyun  阿里云oss  qiniu  七牛 ....',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '相关分类',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '上传时间',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网络地址',
  PRIMARY KEY (`att_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_attachment_category`;
CREATE TABLE `www_sys_attachment_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件管理类型（image,video）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `enname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类目录',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_backup_records
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_backup_records`;
CREATE TABLE `www_sys_backup_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份版本号',
  `backup_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份标识',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份内容',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态',
  `fail_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '失败原因',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `complete_time` int(11) NOT NULL DEFAULT 0 COMMENT '完成时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '备份记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_config
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_config`;
CREATE TABLE `www_sys_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置项关键字',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值json',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用 1启用 0不启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_cron_task
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_cron_task`;
CREATE TABLE `www_sys_cron_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '任务状态',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '执行次数',
  `title` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务模式  cron  定时任务  crond 周期任务',
  `crond_type` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务周期',
  `crond_length` int(11) NOT NULL DEFAULT 0 COMMENT '任务周期',
  `task` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务命令',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附加参数',
  `status_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上次执行结果',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后执行时间',
  `next_time` int(11) NOT NULL DEFAULT 0 COMMENT '下次执行时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 系统任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_dict`;
CREATE TABLE `www_sys_dict`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典关键词',
  `dictionary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典数据',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_export
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_export`;
CREATE TABLE `www_sys_export`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `export_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主题关键字',
  `export_num` int(11) NOT NULL DEFAULT 0 COMMENT '导出数据数量',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件存储路径',
  `file_size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件大小',
  `export_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '导出状态',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '导出时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '导出报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_menu`;
CREATE TABLE `www_sys_menu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `app_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '应用类型',
  `menu_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
  `menu_short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单短标题',
  `menu_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单标识（菜单输入，接口自动生成）',
  `parent_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '父级key',
  `menu_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '菜单类型 0目录 1菜单 2按钮',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标 菜单有效',
  `api_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api接口地址',
  `router_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单路由地址 前端使用',
  `view_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单文件地址',
  `methods` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提交方式POST GET PUT DELETE',
  `sort` int(11) NOT NULL DEFAULT 1 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '正常，禁用（禁用后不允许访问）',
  `is_show` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `delete_time` int(11) NOT NULL DEFAULT 0,
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'system' COMMENT '菜单来源   system 系统文件  create 新建菜单  generator 代码生成器',
  `menu_attr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单属性 common 公共 system 系统',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24079 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice`;
CREATE TABLE `www_sys_notice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标识',
  `sms_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信配置参数',
  `is_wechat` tinyint(4) NOT NULL DEFAULT 0 COMMENT '公众号模板消息（0：关闭，1：开启）',
  `is_weapp` tinyint(4) NOT NULL DEFAULT 0 COMMENT '小程序订阅消息（0：关闭，1：开启）',
  `is_sms` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发送短信（0：关闭，1：开启）',
  `wechat_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信模版消息id',
  `weapp_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信小程序订阅消息id',
  `sms_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信id（对应短信配置）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `wechat_first` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信头部',
  `wechat_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知模型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice_log`;
CREATE TABLE `www_sys_notice_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知记录ID',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '消息key',
  `notice_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'sms' COMMENT '消息类型（sms,wechat.weapp）',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '通知的用户id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '消息的会员id',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接收人用户昵称或姓名',
  `receiver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接收人（对应手机号，openid）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息数据',
  `is_click` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `is_visit` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
  `visit_time` int(11) NOT NULL DEFAULT 0 COMMENT '访问时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息时间',
  `result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结果',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice_sms_log`;
CREATE TABLE `www_sys_notice_sms_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `sms_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送关键字（注册、找回密码）',
  `key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送关键字（注册、找回密码）',
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送内容',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据参数',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'sending' COMMENT '发送状态：sending-发送中；success-发送成功；fail-发送失败',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信结果',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_poster
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_poster`;
CREATE TABLE `www_sys_poster`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报类型',
  `channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报支持渠道',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值json',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用 1启用 2不启用',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `is_default` int(11) NOT NULL DEFAULT 0 COMMENT '是否默认海报，1：是，0：否',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '海报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_printer
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_printer`;
CREATE TABLE `www_sys_printer`  (
  `printer_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `printer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机名称',
  `brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '设备品牌（易联云，365，飞鹅）',
  `printer_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机编号',
  `printer_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机秘钥',
  `open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开发者id',
  `apikey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开发者密钥',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小票打印模板类型，多个逗号隔开',
  `trigger` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '触发打印时机',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '打印模板数据，json格式',
  `print_width` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '58mm' COMMENT '纸张宽度',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0，关闭，1：开启）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`printer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小票打印机' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_printer_template
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_printer_template`;
CREATE TABLE `www_sys_printer_template`  (
  `template_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板类型',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板数据，json格式',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小票打印模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_role`;
CREATE TABLE `www_sys_role`  (
  `role_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色权限(menus_id)',
  `addon_keys` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色应用权限（应用key）',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后修改时间',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_schedule
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_schedule`;
CREATE TABLE `www_sys_schedule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务模板key',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '任务状态 是否启用',
  `time` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务周期  json结构',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '执行次数',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后执行时间',
  `next_time` int(11) NOT NULL DEFAULT 0 COMMENT '下次执行时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_schedule_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_schedule_log`;
CREATE TABLE `www_sys_schedule_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '执行记录id',
  `schedule_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务id',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务模板key',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务名称',
  `execute_time` int(11) NOT NULL COMMENT '执行时间',
  `execute_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '日志信息',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执行状态',
  `class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `job` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '计划任务执行记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_upgrade_records
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_upgrade_records`;
CREATE TABLE `www_sys_upgrade_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `upgrade_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级标识',
  `app_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '升级名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '升级内容',
  `prev_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前一版本',
  `current_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前版本',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态',
  `fail_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `complete_time` int(11) NOT NULL DEFAULT 0 COMMENT '完成时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '升级记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user`;
CREATE TABLE `www_sys_user`  (
  `uid` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '系统用户ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户账号',
  `head_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户密码',
  `real_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际姓名',
  `last_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后一次登录ip',
  `last_time` int(10) NOT NULL DEFAULT 0 COMMENT '最后一次登录时间',
  `create_time` int(10) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `login_count` int(10) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `is_del` tinyint(3) NOT NULL DEFAULT 0,
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(3) NOT NULL DEFAULT 1 COMMENT '后台管理员状态 1有效0无效',
  `role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权限组',
  `is_admin` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是管理员',
  PRIMARY KEY (`uid`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user_log`;
CREATE TABLE `www_sys_user_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员操作记录ID',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录IP',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员姓名',
  `url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求方式',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 896 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user_role`;
CREATE TABLE `www_sys_user_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_admin` int(11) NOT NULL DEFAULT 0 COMMENT '是否是超级管理员',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_verifier
-- ----------------------------
DROP TABLE IF EXISTS `www_verifier`;
CREATE TABLE `www_verifier`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `verify_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_verify
-- ----------------------------
DROP TABLE IF EXISTS `www_verify`;
CREATE TABLE `www_verify`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销参数',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销类型',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '核销时间',
  `verifier_member_id` int(11) NOT NULL DEFAULT 0 COMMENT '核销会员id',
  `value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销内容',
  `body` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `relate_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_weapp_version
-- ----------------------------
DROP TABLE IF EXISTS `www_weapp_version`;
CREATE TABLE `www_weapp_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `version_no` int(11) NOT NULL DEFAULT 1,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `task_key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上传任务key',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序版本' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_web_adv
-- ----------------------------
DROP TABLE IF EXISTS `www_web_adv`;
CREATE TABLE `www_web_adv`  (
  `adv_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `adv_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '广告位key',
  `adv_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告内容描述',
  `adv_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告链接',
  `adv_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告内容图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `background` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '#FFFFFF' COMMENT '背景色',
  PRIMARY KEY (`adv_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_web_friendly_link
-- ----------------------------
DROP TABLE IF EXISTS `www_web_friendly_link`;
CREATE TABLE `www_web_friendly_link`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '索引id',
  `link_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `link_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `link_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `is_show` int(11) NOT NULL DEFAULT 1 COMMENT '是否显示 1.是 2.否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电脑端友情链接表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_web_nav
-- ----------------------------
DROP TABLE IF EXISTS `www_web_nav`;
CREATE TABLE `www_web_nav`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nav_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '导航名称',
  `nav_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接地址',
  `sort` int(11) NOT NULL COMMENT '排序号',
  `is_blank` int(11) NULL DEFAULT 0 COMMENT '是否新打开',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '修改时间',
  `is_show` smallint(6) NOT NULL DEFAULT 1 COMMENT '是否显示 1显示 0不显示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'PC导航管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_wechat_fans
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_fans`;
CREATE TABLE `www_wechat_fans`  (
  `fans_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '粉丝ID',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `sex` smallint(6) NOT NULL DEFAULT 1 COMMENT '性别',
  `language` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户语言',
  `country` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '国家',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '行政区/县',
  `openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的标识，对当前公众号唯一     用户的唯一身份ID',
  `unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '粉丝unionid',
  `groupid` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝所在组id',
  `is_subscribe` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否订阅',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `subscribe_time` int(11) NOT NULL DEFAULT 0 COMMENT '关注时间',
  `subscribe_scene` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '返回用户关注的渠道来源',
  `unsubscribe_time` int(11) NOT NULL DEFAULT 0 COMMENT '取消关注时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝信息最后更新时间',
  `app_id` int(11) NOT NULL DEFAULT 0 COMMENT '应用appid',
  PRIMARY KEY (`fans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信粉丝列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_wechat_media
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_media`;
CREATE TABLE `www_wechat_media`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '值',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `media_id` varchar(70) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '微信端返回的素材id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信素材表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_reply`;
CREATE TABLE `www_wechat_reply`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则名称',
  `keyword` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词',
  `reply_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复类型 subscribe-关注回复 keyword-关键字回复 default-默认回复',
  `matching_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '匹配方式：full 全匹配；like-模糊匹配',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复内容',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 50 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `reply_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复方式 all 全部 rand随机',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公众号消息回调表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_brand
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_brand`;
CREATE TABLE `www_yy_phone_brand`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌首字母',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌图标',
  `bg_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#f5f5f5' COMMENT '品牌背景色',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门：0=否，1=是',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `display_mode` tinyint(1) NULL DEFAULT 0 COMMENT '显示模式：0=显示型号列表，1=跳转其他页面',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '一级分类ID',
  `subcategory_id` int(11) NOT NULL DEFAULT 0 COMMENT '二级分类ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_subcategory_id`(`subcategory_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_brand_category
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_brand_category`;
CREATE TABLE `www_yy_phone_brand_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `brand_id` int(11) NOT NULL COMMENT '品牌ID',
  `category_id` int(11) NOT NULL COMMENT '一级分类ID',
  `subcategory_id` int(11) NULL DEFAULT NULL COMMENT '二级分类ID',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_unique`(`brand_id`, `category_id`, `subcategory_id`) USING BTREE,
  INDEX `idx_category`(`category_id`) USING BTREE,
  INDEX `idx_subcategory`(`subcategory_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌分类关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_category
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_category`;
CREATE TABLE `www_yy_phone_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `display_mode` tinyint(1) NULL DEFAULT 0 COMMENT '显示模式：0=显示二级分类，1=直接显示品牌，2=跳转其他页面',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收一级分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_answer
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_answer`;
CREATE TABLE `www_yy_phone_evaluation_answer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `record_id` int(11) NOT NULL COMMENT '估价记录ID',
  `template_question_id` int(11) NOT NULL COMMENT '模板题目ID',
  `template_option_id` int(11) NOT NULL COMMENT '模板选项ID',
  `question_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `option_text` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项文本',
  `option_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项值',
  `price_deduction` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '减价金额',
  `answer_time` int(11) NULL DEFAULT NULL COMMENT '答题时间（秒）',
  `step_number` int(11) NOT NULL COMMENT '步骤序号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_record_id`(`record_id`) USING BTREE,
  INDEX `idx_template_question_id`(`template_question_id`) USING BTREE,
  INDEX `idx_template_option_id`(`template_option_id`) USING BTREE,
  INDEX `idx_step_number`(`step_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价答案详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_config
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_config`;
CREATE TABLE `www_yy_phone_evaluation_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置描述',
  `group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置分组',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key`) USING BTREE,
  INDEX `idx_group_name`(`group_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_option_category
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_option_category`;
CREATE TABLE `www_yy_phone_evaluation_option_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分类描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类颜色',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '选项分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_option_library
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_option_library`;
CREATE TABLE `www_yy_phone_evaluation_option_library`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `category_id` int(11) NULL DEFAULT NULL COMMENT '选项分类ID',
  `option_text` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项文本',
  `option_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项值',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '选项描述',
  `icon_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '图标类型：0无图标 1系统图标 2自定义图片',
  `icon_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标值（图标名称或图片URL）',
  `default_price_deduction` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '默认减价金额',
  `deduction_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '减价类型：1固定金额 2百分比',
  `color_scheme` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '颜色方案',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签（JSON格式）',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '选项库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_question_category
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_question_category`;
CREATE TABLE `www_yy_phone_evaluation_question_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '父分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分类描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类颜色',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_question_library
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_question_library`;
CREATE TABLE `www_yy_phone_evaluation_question_library`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `category_id` int(11) NULL DEFAULT NULL COMMENT '题目分类ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目描述/说明',
  `question_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '题目类型：1单选 2多选 3判断',
  `layout_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '布局类型：1列表 2网格 3卡片',
  `is_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否必答：1是 0否',
  `help_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '帮助说明文字',
  `help_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '帮助图片',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签（JSON格式）',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category_id`) USING BTREE,
  INDEX `idx_type`(`question_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_record
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_record`;
CREATE TABLE `www_yy_phone_evaluation_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `record_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '估价单号',
  `template_id` int(11) NOT NULL COMMENT '使用的模板ID',
  `model_id` int(11) NOT NULL COMMENT '手机型号ID',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户ID（可为空）',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话ID',
  `base_price` decimal(10, 2) NOT NULL COMMENT '基础价格',
  `total_deduction` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总减价金额',
  `final_price` decimal(10, 2) NOT NULL COMMENT '最终估价',
  `completion_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '完成状态：0进行中 1已完成 2已放弃',
  `completion_time` int(11) NULL DEFAULT NULL COMMENT '完成时间（秒）',
  `question_count` int(11) NOT NULL DEFAULT 0 COMMENT '回答题目数',
  `answers_summary` json NULL COMMENT '答案摘要（JSON格式）',
  `device_info` json NULL COMMENT '设备信息',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户代理',
  `referrer` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源页面',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_record_no`(`record_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_model_id`(`model_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_session_id`(`session_id`) USING BTREE,
  INDEX `idx_completion_status`(`completion_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_template
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_template`;
CREATE TABLE `www_yy_phone_evaluation_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `model_id` int(11) NOT NULL COMMENT '关联手机型号ID',
  `base_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '基础估价（最高价）',
  `min_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低估价',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板描述',
  `first_question_id` int(11) NULL DEFAULT NULL COMMENT '第一题ID',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数',
  `avg_completion_time` int(11) NULL DEFAULT NULL COMMENT '平均完成时间（秒）',
  `completion_rate` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '完成率（%）',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_by` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` int(11) NULL DEFAULT NULL COMMENT '更新人ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_model_id`(`model_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_template_option
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_template_option`;
CREATE TABLE `www_yy_phone_evaluation_template_option`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `template_question_id` int(11) NOT NULL COMMENT '模板题目ID',
  `option_library_id` int(11) NOT NULL COMMENT '选项库ID',
  `option_text` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项文本（可自定义）',
  `option_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项值',
  `price_deduction` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '减价金额',
  `deduction_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '减价类型：1固定金额 2百分比',
  `is_final` tinyint(1) NOT NULL DEFAULT 0 COMMENT '跳转目标：0继续下一题 1结束流程 2无法回收',
  `next_question_id` int(11) NULL DEFAULT NULL COMMENT '下一题ID（当is_final=0时）',
  `describe` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `condition_rules` json NULL COMMENT '显示条件规则',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `display_config` json NULL COMMENT '配置信息',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_question_id`(`template_question_id`) USING BTREE,
  INDEX `idx_option_library_id`(`option_library_id`) USING BTREE,
  INDEX `idx_next_question_id`(`next_question_id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 72 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板选项关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_evaluation_template_question
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_evaluation_template_question`;
CREATE TABLE `www_yy_phone_evaluation_template_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `template_id` int(11) NOT NULL COMMENT '模板ID',
  `question_library_id` int(11) NULL DEFAULT 0 COMMENT '题目库ID（自定义题目为0）',
  `question_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目标题（可自定义）',
  `question_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目描述（可自定义）',
  `layout_config` json NULL COMMENT '布局配置（选项排列方式等）',
  `validation_rules` json NULL COMMENT '验证规则',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_question_library_id`(`question_library_id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_question_library_id_status`(`question_library_id`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板题目关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_model
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_model`;
CREATE TABLE `www_yy_phone_model`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '型号ID',
  `brand_id` int(11) NOT NULL COMMENT '品牌ID',
  `series_id` int(11) NULL DEFAULT NULL COMMENT '系列ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '型号名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号图片',
  `max_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '最高回收价',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门：0=否，1=是',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_series_id`(`series_id`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '型号表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store`;
CREATE TABLE `www_yy_phone_recycle_store`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `store_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '门店编码',
  `store_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '门店名称',
  `store_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '门店类型：1直营店 2加盟店 3合作店',
  `manager_id` int(11) NULL DEFAULT NULL COMMENT '店长用户ID',
  `contact_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '联系电话',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省份ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县ID',
  `province` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '区县',
  `address` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '详细地址',
  `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
  `business_hours` json NULL COMMENT '营业时间配置',
  `business_license` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '营业执照',
  `store_images` json NULL COMMENT '门店图片',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '门店描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0停业',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_store_code`(`store_code`) USING BTREE,
  INDEX `idx_manager_id`(`manager_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_location`(`longitude`, `latitude`) USING BTREE,
  INDEX `idx_province_id`(`province_id`) USING BTREE,
  INDEX `idx_city_id`(`city_id`) USING BTREE,
  INDEX `idx_district_id`(`district_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_config
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_config`;
CREATE TABLE `www_yy_phone_recycle_store_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `config_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配置值',
  `config_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '配置类型：pricing定价、workflow流程、notification通知等',
  `description` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统配置：1是 0否',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_store_config`(`store_id`, `config_key`) USING BTREE,
  INDEX `idx_config_type`(`config_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_customer
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_customer`;
CREATE TABLE `www_yy_phone_recycle_store_customer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `customer_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户手机',
  `customer_id_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `gender` tinyint(1) NULL DEFAULT NULL COMMENT '性别：1男 2女',
  `age_range` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '年龄段',
  `address` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
  `customer_level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '客户等级：1普通 2VIP 3黄金 4钻石',
  `total_orders` int(11) NOT NULL DEFAULT 0 COMMENT '总订单数',
  `total_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总交易金额',
  `last_visit_time` datetime NULL DEFAULT NULL COMMENT '最后访问时间',
  `customer_source` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户来源',
  `preferences` json NULL COMMENT '偏好设置',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0黑名单',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_customer_phone`(`customer_phone`) USING BTREE,
  INDEX `idx_customer_level`(`customer_level`) USING BTREE,
  INDEX `idx_last_visit_time`(`last_visit_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店客户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_daily_report
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_daily_report`;
CREATE TABLE `www_yy_phone_recycle_store_daily_report`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日报ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `report_date` date NOT NULL COMMENT '报告日期',
  `staff_id` int(11) NOT NULL COMMENT '制表员工ID',
  `total_orders` int(11) NOT NULL DEFAULT 0 COMMENT '总订单数',
  `completed_orders` int(11) NOT NULL DEFAULT 0 COMMENT '完成订单数',
  `cancelled_orders` int(11) NOT NULL DEFAULT 0 COMMENT '取消订单数',
  `total_revenue` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总收入',
  `total_cost` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总成本',
  `gross_profit` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '毛利润',
  `cash_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '现金金额',
  `transfer_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '转账金额',
  `inventory_in` int(11) NOT NULL DEFAULT 0 COMMENT '入库数量',
  `inventory_out` int(11) NOT NULL DEFAULT 0 COMMENT '出库数量',
  `customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '客户数量',
  `new_customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '新客户数量',
  `staff_performance` json NULL COMMENT '员工业绩统计',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0作废',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_store_date`(`store_id`, `report_date`) USING BTREE,
  INDEX `idx_report_date`(`report_date`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店日报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_equipment
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_equipment`;
CREATE TABLE `www_yy_phone_recycle_store_equipment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `equipment_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备编码',
  `equipment_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `equipment_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型：检测设备、收银设备等',
  `brand` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '品牌',
  `model` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '型号',
  `serial_number` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '序列号',
  `purchase_date` date NULL DEFAULT NULL COMMENT '购买日期',
  `purchase_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '购买价格',
  `warranty_period` int(11) NULL DEFAULT NULL COMMENT '保修期（月）',
  `maintenance_cycle` int(11) NULL DEFAULT NULL COMMENT '维护周期（天）',
  `last_maintenance` date NULL DEFAULT NULL COMMENT '最后维护日期',
  `next_maintenance` date NULL DEFAULT NULL COMMENT '下次维护日期',
  `equipment_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '设备状态：1正常 2故障 3维修中 4报废',
  `responsible_staff_id` int(11) NULL DEFAULT NULL COMMENT '负责人员工ID',
  `location` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备位置',
  `specifications` json NULL COMMENT '设备规格参数',
  `maintenance_records` json NULL COMMENT '维护记录',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_equipment_code`(`equipment_code`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_equipment_status`(`equipment_status`) USING BTREE,
  INDEX `idx_responsible_staff_id`(`responsible_staff_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店设备表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_finance
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_finance`;
CREATE TABLE `www_yy_phone_recycle_store_finance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `staff_id` int(11) NOT NULL COMMENT '操作员工ID',
  `order_id` int(11) NULL DEFAULT NULL COMMENT '关联订单ID',
  `finance_type` tinyint(1) NOT NULL COMMENT '财务类型：1收入 2支出 3转账',
  `category` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类：回收收入、销售收入、运营支出等',
  `amount` decimal(10, 2) NOT NULL COMMENT '金额',
  `payment_method` tinyint(1) NOT NULL COMMENT '支付方式：1现金 2银行转账 3支付宝 4微信',
  `account_info` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账户信息',
  `description` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
  `voucher_images` json NULL COMMENT '凭证图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0作废',
  `finance_date` date NOT NULL COMMENT '财务日期',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_finance_type`(`finance_type`) USING BTREE,
  INDEX `idx_finance_date`(`finance_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店财务记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_inventory
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_inventory`;
CREATE TABLE `www_yy_phone_recycle_store_inventory`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `phone_model_id` int(11) NOT NULL COMMENT '手机型号ID',
  `phone_imei` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机IMEI',
  `phone_color` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机颜色',
  `phone_storage` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储容量',
  `quality_grade` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '成色等级',
  `purchase_price` decimal(10, 2) NOT NULL COMMENT '收购价格',
  `estimated_sell_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预估销售价格',
  `inventory_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '库存状态：1在库 2已出库 3已销售 4报废',
  `storage_location` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存放位置',
  `quality_check` json NULL COMMENT '质检记录',
  `accessories` json NULL COMMENT '配件清单',
  `images` json NULL COMMENT '库存照片',
  `in_time` datetime NOT NULL COMMENT '入库时间',
  `out_time` datetime NULL DEFAULT NULL COMMENT '出库时间',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_phone_imei`(`phone_imei`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_phone_model_id`(`phone_model_id`) USING BTREE,
  INDEX `idx_inventory_status`(`inventory_status`) USING BTREE,
  INDEX `idx_in_time`(`in_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店库存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_log
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_log`;
CREATE TABLE `www_yy_phone_recycle_store_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `staff_id` int(11) NOT NULL COMMENT '操作员工ID',
  `action_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作类型：order订单、inventory库存、finance财务等',
  `action_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作名称',
  `target_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目标类型',
  `target_id` int(11) NULL DEFAULT NULL COMMENT '目标ID',
  `old_data` json NULL COMMENT '操作前数据',
  `new_data` json NULL COMMENT '操作后数据',
  `ip_address` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户代理',
  `result` tinyint(1) NOT NULL DEFAULT 1 COMMENT '操作结果：1成功 0失败',
  `error_message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE,
  INDEX `idx_action_type`(`action_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_notification
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_notification`;
CREATE TABLE `www_yy_phone_recycle_store_notification`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `store_id` int(11) NULL DEFAULT NULL COMMENT '门店ID（NULL表示全部门店）',
  `staff_id` int(11) NULL DEFAULT NULL COMMENT '员工ID（NULL表示门店所有员工）',
  `title` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通知内容',
  `notification_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '通知类型：system系统、order订单、inventory库存、finance财务等',
  `priority` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优先级：1普通 2重要 3紧急',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：1已读 0未读',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `extra_data` json NULL COMMENT '额外数据',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE,
  INDEX `idx_notification_type`(`notification_type`) USING BTREE,
  INDEX `idx_is_read`(`is_read`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店通知消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_order
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_order`;
CREATE TABLE `www_yy_phone_recycle_store_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单编号',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `staff_id` int(11) NOT NULL COMMENT '处理员工ID',
  `customer_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户手机',
  `customer_id_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户身份证',
  `phone_model_id` int(11) NOT NULL COMMENT '手机型号ID',
  `phone_imei` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机IMEI',
  `phone_color` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机颜色',
  `phone_storage` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储容量',
  `evaluation_template_id` int(11) NOT NULL COMMENT '评估模板ID',
  `evaluation_record_id` int(11) NULL DEFAULT NULL COMMENT '评估记录ID',
  `estimated_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预估价格',
  `final_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最终成交价格',
  `price_adjustment` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格调整',
  `adjustment_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '调价原因',
  `payment_method` tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付方式：1现金 2银行转账 3支付宝 4微信',
  `payment_account` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付账户',
  `order_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态：1待评估 2已评估 3已成交 4已取消',
  `trade_time` datetime NULL DEFAULT NULL COMMENT '成交时间',
  `phone_images` json NULL COMMENT '手机照片',
  `accessories` json NULL COMMENT '配件信息',
  `quality_issues` json NULL COMMENT '质量问题记录',
  `remarks` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_staff_id`(`staff_id`) USING BTREE,
  INDEX `idx_customer_phone`(`customer_phone`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE,
  INDEX `idx_trade_time`(`trade_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店回收订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_recycle_store_staff
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_recycle_store_staff`;
CREATE TABLE `www_yy_phone_recycle_store_staff`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `staff_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工编号',
  `real_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '手机号',
  `id_card` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `position` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '职位',
  `role_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '角色类型：1店长 2收银员 3评估师 4普通员工',
  `permissions` json NULL COMMENT '权限配置',
  `entry_date` date NOT NULL COMMENT '入职日期',
  `salary_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '薪资类型：1固定工资 2提成制 3混合制',
  `base_salary` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '基础工资',
  `commission_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '提成比例(%)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1在职 0离职',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_staff_code`(`staff_code`) USING BTREE,
  INDEX `idx_store_id`(`store_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店员工表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yy_phone_series
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_series`;
CREATE TABLE `www_yy_phone_series`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '系列ID',
  `brand_id` int(11) NOT NULL COMMENT '所属品牌ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系列名称',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '型号系列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yy_phone_subcategory
-- ----------------------------
DROP TABLE IF EXISTS `www_yy_phone_subcategory`;
CREATE TABLE `www_yy_phone_subcategory`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '二级分类ID',
  `category_id` int(11) NOT NULL COMMENT '所属一级分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `display_mode` tinyint(1) NULL DEFAULT 0 COMMENT '显示模式：0=显示品牌列表，1=跳转其他页面',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收二级分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_brands
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_brands`;
CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_categories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_categories`;
CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_category_accessories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_category_accessories`;
CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_category_photos
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_category_photos`;
CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_goods`;
CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_order_status_logs
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_order_status_logs`;
CREATE TABLE `www_yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  CONSTRAINT `fk_status_logs_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单状态变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_accessories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_accessories`;
CREATE TABLE `www_yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE,
  CONSTRAINT `fk_quote_accessories_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_orders
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_orders`;
CREATE TABLE `www_yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_photos
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_photos`;
CREATE TABLE `www_yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE,
  CONSTRAINT `fk_quote_photos_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_records
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_records`;
CREATE TABLE `www_yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE,
  CONSTRAINT `fk_quote_records_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_recycle_standards
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_standards`;
CREATE TABLE `www_yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher`;
CREATE TABLE `www_yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher_send_log
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher_send_log`;
CREATE TABLE `www_yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher_template
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher_template`;
CREATE TABLE `www_yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Triggers structure for table www_yz_she_quote_orders
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time`;
delimiter ;;
CREATE TRIGGER `tr_quote_orders_auto_cancel_time` BEFORE UPDATE ON `www_yz_she_quote_orders` FOR EACH ROW BEGIN
  -- 当状态从待确认(2)变为待发货(3)时，设置48小时后自动取消时间
  IF OLD.status = 2 AND NEW.status = 3 THEN
    SET NEW.auto_cancel_time = DATE_ADD(NOW(), INTERVAL 48 HOUR);
  END IF;

  -- 当状态变为已完成(4)或已取消(5)时，清除自动取消时间
  IF NEW.status IN (4, 5) THEN
    SET NEW.auto_cancel_time = NULL;
  END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
