import request from '@/utils/request'

/**
 * 获取回收订单列表
 */
export function getRecycleOrderList(params: Record<string, any>) {
    return request.get(`yz_she/recycle_order/lists`, { params })
}

/**
 * 获取回收订单详情
 */
export function getRecycleOrderInfo(id: number) {
    return request.get(`yz_she/recycle_order/info/${id}`)
}

/**
 * 收货确认
 */
export function receiveRecycleOrder(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/receive/${id}`, params)
}

/**
 * 开始质检
 */
export function startQualityCheck(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/start_quality/${id}`, params)
}

/**
 * 完成质检
 */
export function completeQualityCheck(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/complete_quality/${id}`, params)
}

/**
 * 结算订单
 */
export function settlementRecycleOrder(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/settlement/${id}`, params)
}

/**
 * 退回订单
 */
export function returnRecycleOrder(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/return/${id}`, params)
}

/**
 * 获取状态选项
 */
export function getRecycleOrderStatusOptions() {
    return request.get(`yz_she/recycle_order/get_status_options`)
}

/**
 * 获取订单来源选项
 */
export function getRecycleOrderSourceTypeOptions() {
    return request.get(`yz_she/recycle_order/get_source_type_options`)
}

/**
 * 获取配送方式选项
 */
export function getRecycleOrderDeliveryTypeOptions() {
    return request.get(`yz_she/recycle_order/get_delivery_type_options`)
}

/**
 * 批量操作
 */
export function batchRecycleOrder(params: Record<string, any>) {
    return request.post(`yz_she/recycle_order/batch`, params)
}

/**
 * 获取状态统计数据
 */
export function getRecycleOrderStatusCounts() {
    return request.get(`yz_she/recycle_order/status_counts`)
}

/**
 * 更新快递信息
 */
export function updateExpressInfo(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/express/${id}`, params)
}

/**
 * 更新订单备注
 */
export function updateOrderNotes(id: number, params: Record<string, any>) {
    return request.put(`yz_she/recycle_order/notes/${id}`, params)
}

/**
 * 获取订单操作日志
 */
export function getOrderLogs(id: number) {
    return request.get(`yz_she/recycle_order/logs/${id}`)
}

/**
 * 导出数据
 */
export function exportRecycleOrder(params: Record<string, any>) {
    return request.get(`yz_she/recycle_order/export`, {
        params,
        responseType: 'blob'
    })
}
