<template>
  <view class="photo-evaluate-page">


    <!-- 品牌信息区域 -->
    <view class="brand-section">
      <view class="brand-info">
        <view class="brand-logo">
          <image
            v-if="brandInfo.image"
            :src="img(brandInfo.image)"
            class="logo-image"
            mode="aspectFit"
          />
          <view v-else class="brand-placeholder">
            <text class="placeholder-text">暂无图片</text>
          </view>
        </view>
        <view class="brand-details">
          <text class="brand-name">{{ brandInfo.name }}</text>
          <text class="brand-status">{{ brandInfo.status }}</text>
          <text v-if="productInfo.name" class="product-name">{{ productInfo.name }}</text>
        </view>
      </view>
      <view class="brand-bg">
        <view class="bg-circle"></view>
      </view>
    </view>

    <!-- 实物照片区域 -->
    <view class="photo-section">
      <view class="section-header">
        <text class="section-title">实物照片</text>
        <text class="section-desc">请按照要求上传清晰照片</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <text class="loading-text">正在加载配置...</text>
      </view>

      <!-- 照片上传网格 -->
      <view v-else class="photo-grid">
        <view
          class="photo-item"
          v-for="(photoConfig, index) in photoConfigs"
          :key="photoConfig.id"
          @click="takePhoto(photoConfig.id, index)"
        >
          <view
            class="photo-placeholder"
            :class="{
              uploaded: photos[photoConfig.id],
              uploading: uploadingPhotos.has(photoConfig.id)
            }"
            :style="{ backgroundImage: photoConfig.background_image ? `url(${img(photoConfig.background_image)})` : '' }"
          >
            <image
              v-if="photos[photoConfig.id]"
              :src="img(photos[photoConfig.id])"
              class="photo-image"
              mode="aspectFill"
            ></image>
            <view v-else-if="uploadingPhotos.has(photoConfig.id)" class="uploading-content">
              <view class="uploading-spinner"></view>
              <text class="uploading-text">上传中...</text>
            </view>
            <view v-else class="placeholder-content">
              <!-- 移除拍照图标，只保留背景 -->
            </view>
          </view>
          <text class="photo-label">{{ photoConfig.photo_name }}</text>
        </view>
      </view>
    </view>

    <!-- 明显瑕疵区域 -->
    <view class="defect-section">
      <view class="section-header">
        <text class="section-title">明显瑕疵(最多6张)</text>
      </view>
      
      <view class="defect-photos">
        <view class="defect-item" @click="takeDefectPhoto">
          <view class="defect-placeholder" :class="{ uploading: uploadingDefectPhotos }">
            <view v-if="uploadingDefectPhotos" class="uploading-content">
              <view class="uploading-spinner"></view>
              <text class="uploading-text">上传中...</text>
            </view>
            <view v-else class="placeholder-content">
              <!-- 移除加号图标，只保留背景 -->
            </view>
          </view>
          <text class="defect-label">{{ uploadingDefectPhotos ? '上传中' : '新增' }}</text>
        </view>
        
        <view 
          class="defect-item" 
          v-for="(photo, index) in defectPhotos" 
          :key="index"
          @click="viewDefectPhoto(index)"
        >
          <view class="defect-placeholder uploaded">
            <image :src="img(photo)" class="defect-image" mode="aspectFill"></image>
            <view class="delete-btn" @click.stop="deleteDefectPhoto(index)">
              <u-icon name="close" color="#fff" size="16"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他配件区域 -->
    <view class="accessories-section">
      <view class="section-header">
        <text class="section-title">其他</text>
        <text class="section-desc">请配置配件获得更高价</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <text class="loading-text">正在加载配置...</text>
      </view>

      <!-- 配件网格 -->
      <view v-else-if="accessoryConfigs.length > 0" class="accessories-grid">
        <view
          class="accessory-item"
          v-for="accessory in accessoryConfigs"
          :key="accessory.id"
          :class="{ selected: accessory.selected }"
          @click="toggleAccessory(accessory)"
        >
          <text class="accessory-name">{{ accessory.accessory_name }}</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无配件配置</text>
      </view>
    </view>

    <!-- 备注区域 -->
    <view class="note-section">
      <view class="section-header">
        <text class="section-title">备注</text>
      </view>
      <textarea
        class="note-textarea"
        v-model="note"
        placeholder="您可以填写其他明显瑕疵备注、使用情况、是否有包装等信息，帮助我们更好的估价。"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 底部提交按钮 -->
    <view class="bottom-action">
      <view class="submit-button" @click="submitEvaluation" :class="{ disabled: !canSubmit }">
        <text class="submit-text">免费评估</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { img } from '@/utils/common'
import { getCategoryConfig } from '@/addon/yz_she/api/category'
import { createQuoteOrder } from '@/addon/yz_she/api/quote'
import { uploadImage } from '@/app/api/system'
import useMemberStore from '@/stores/member'
import { useLogin } from '@/hooks/useLogin'

// 响应式数据
const memberStore = useMemberStore()
const userInfo = computed(() => memberStore.info)

// 页面参数
const pageParams = ref({
  from: '', // 来源页面：index 或 detail
  brandId: '', // 品牌ID
  brandName: '', // 品牌名称
  brandLogo: '', // 品牌Logo
  productId: '', // 商品ID（从detail跳转时）
  productName: '', // 商品名称
  productImage: '', // 商品图片
  categoryId: '' // 分类ID
})

// 品牌信息
const brandInfo = ref({
  id: '',
  name: '请选择品牌',
  image: '',
  status: '未选择品牌'
})

// 商品信息（从detail页面跳转时使用）
const productInfo = ref({
  id: '',
  name: '',
  image: '',
  category_id: ''
})

// 照片配置数据
const photoConfigs = ref([])

// 配件配置数据
const accessoryConfigs = ref([])

// 照片数据（动态生成）
const photos = ref({})

// 瑕疵照片
const defectPhotos = ref<string[]>([])

// 加载状态
const loading = ref(false)

// 上传状态管理
const uploadingPhotos = ref(new Set())
const uploadingDefectPhotos = ref(false)

// 备注内容
const note = ref('')

// 计算是否可以提交
const canSubmit = computed(() => {
  // 检查所有必需的照片是否都已上传
  return photoConfigs.value.every(config => photos.value[config.id])
})



// 拍照功能
const takePhoto = (photoId: number, index: number) => {
  // 如果正在上传，则不允许重复操作
  if (uploadingPhotos.value.has(photoId)) {
    uni.showToast({
      title: '正在上传中，请稍候',
      icon: 'none'
    })
    return
  }

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const tempFilePath = res.tempFilePaths[0]

        // 添加到上传状态
        uploadingPhotos.value.add(photoId)

        // 显示上传中状态
        uni.showLoading({
          title: '上传中...',
          mask: true
        })

        // 调用系统上传接口
        uploadImage({
          filePath: tempFilePath,
          name: 'file'
        }).then((uploadResult) => {
          if (uploadResult.code === 1) {
            // 保存上传后的图片路径
            photos.value[photoId] = uploadResult.data.url
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(uploadResult.msg || '上传失败')
          }
        }).catch((uploadError) => {
          console.error('上传图片失败:', uploadError)
          uni.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          })
        }).finally(() => {
          // 移除上传状态
          uploadingPhotos.value.delete(photoId)
          uni.hideLoading()
        })
      }
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 拍摄瑕疵照片
const takeDefectPhoto = () => {
  if (defectPhotos.value.length >= 6) {
    uni.showToast({
      title: '最多只能上传6张瑕疵照片',
      icon: 'none'
    })
    return
  }

  // 如果正在上传，则不允许重复操作
  if (uploadingDefectPhotos.value) {
    uni.showToast({
      title: '正在上传中，请稍候',
      icon: 'none'
    })
    return
  }

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        const tempFilePath = res.tempFilePaths[0]

        // 设置上传状态
        uploadingDefectPhotos.value = true

        // 显示上传中状态
        uni.showLoading({
          title: '上传中...',
          mask: true
        })

        // 调用系统上传接口
        uploadImage({
          filePath: tempFilePath,
          name: 'file'
        }).then((uploadResult) => {
          if (uploadResult.code === 1) {
            // 保存上传后的图片路径
            defectPhotos.value.push(uploadResult.data.url)
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(uploadResult.msg || '上传失败')
          }
        }).catch((uploadError) => {
          console.error('上传瑕疵照片失败:', uploadError)
          uni.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          })
        }).finally(() => {
          // 重置上传状态
          uploadingDefectPhotos.value = false
          uni.hideLoading()
        })
      }
    },
    fail: (error) => {
      console.error('选择瑕疵照片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 查看瑕疵照片
const viewDefectPhoto = (index: number) => {
  const urls = defectPhotos.value.map(photo => img(photo))
  uni.previewImage({
    urls: urls,
    current: index
  })
}

// 删除瑕疵照片
const deleteDefectPhoto = (index: number) => {
  defectPhotos.value.splice(index, 1)
}

// 切换配件选择
const toggleAccessory = (accessory: any) => {
  accessory.selected = !accessory.selected
}

// 提交评估
const submitEvaluation = async () => {
  // 检查是否登录
  if (!userInfo.value) {
    // 保存当前页面参数到登录返回信息中
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    useLogin().setLoginBack({
      url: '/addon/yz_she/pages/evaluate/photo',
      param: options
    })
    return false
  }

  if (!canSubmit.value) {
    uni.showToast({
      title: '请先上传所有必需的照片',
      icon: 'none'
    })
    return
  }

  if (loading.value) {
    return
  }

  loading.value = true

  try {
    const selectedAccessories = accessoryConfigs.value.filter(item => item.selected)

    const submitData = {
      category_id: parseInt(pageParams.value.categoryId),
      brand_id: brandInfo.value?.id || 0,
      product_id: brandInfo.value?.product_id || 0,
      product_name: brandInfo.value?.name || '',
      product_code: brandInfo.value?.code || '',
      product_image: brandInfo.value?.image || '',
      photos: photos.value,
      defect_photos: defectPhotos.value,
      accessories: selectedAccessories,
      note: note.value
    }

    console.log('提交评估数据:', submitData)

    const response = await createQuoteOrder(submitData)

    if (response.code === 1) {
      uni.showToast({
        title: '评估提交成功',
        icon: 'success'
      })

      // 延迟跳转到订单详情页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/addon/yz_she/pages/order/detail/quote-detail?id=${response.data.order_id}`
        })
      }, 1500)
    } else {
      uni.showToast({
        title: response.msg || '提交失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('提交评估失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载分类配置
const loadCategoryConfig = async (categoryId: string) => {
  if (!categoryId) {
    console.warn('分类ID为空，无法加载配置')
    return
  }

  try {
    loading.value = true
    const res = await getCategoryConfig(parseInt(categoryId))

    if (res.data) {
      // 设置照片配置
      photoConfigs.value = res.data.photos || []

      // 初始化照片数据对象
      const photoData = {}
      photoConfigs.value.forEach(config => {
        photoData[config.id] = ''
      })
      photos.value = photoData

      // 设置配件配置
      accessoryConfigs.value = (res.data.accessories || []).map(item => ({
        ...item,
        selected: false
      }))

      console.log('分类配置加载成功:', res.data)
    }
  } catch (error) {
    console.error('加载分类配置失败:', error)
    uni.showToast({
      title: '配置加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时获取参数
onMounted(() => {
  // 检查是否登录，未登录则直接跳转
  if (!userInfo.value) {
    console.log('用户未登录，跳转到登录页面')
    // 保存当前页面参数到登录返回信息中
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    useLogin().setLoginBack({
      url: '/addon/yz_she/pages/evaluate/photo',
      param: options
    })
    return
  }

  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  console.log('拍照估价页面URL参数:', options)

  // 尝试从本地缓存获取跳转数据
  const cachedData = uni.getStorageSync('photoEvaluateData')
  console.log('缓存的跳转数据:', cachedData)

  // 优先使用缓存数据，如果没有则使用URL参数
  if (cachedData && cachedData.from) {
    pageParams.value = {
      from: cachedData.from || '',
      brandId: cachedData.brandId || '',
      brandName: cachedData.brandName || '',
      brandLogo: cachedData.brandLogo || '',
      productId: cachedData.productId || '',
      productName: cachedData.productName || '',
      productImage: cachedData.productImage || '',
      categoryId: cachedData.categoryId || ''
    }
    console.log('使用缓存数据:', pageParams.value)

    // 清除缓存数据，避免下次误用
    uni.removeStorageSync('photoEvaluateData')
  } else {
    // 降级使用URL参数
    pageParams.value = {
      from: options.from || '',
      brandId: options.brandId || '',
      brandName: decodeURIComponent(options.brandName || ''),
      brandLogo: decodeURIComponent(options.brandLogo || ''),
      productId: options.productId || '',
      productName: decodeURIComponent(options.productName || ''),
      productImage: decodeURIComponent(options.productImage || ''),
      categoryId: options.categoryId || ''
    }
    console.log('使用URL参数:', pageParams.value)
  }

  // 根据来源设置对应数据
  if (pageParams.value.from === 'index') {
    if (pageParams.value.brandId) {
      setBrandInfoFromIndex()
    } else {
      console.warn('从index跳转但缺少brandId参数')
      uni.showToast({
        title: '品牌信息获取失败',
        icon: 'none'
      })
    }
  } else if (pageParams.value.from === 'detail') {
    if (pageParams.value.productId) {
      setBrandInfoFromDetail()
    } else {
      console.warn('从detail跳转但缺少productId参数')
      uni.showToast({
        title: '商品信息获取失败',
        icon: 'none'
      })
    }
  } else {
    console.log('未识别的跳转来源或参数缺失')
    uni.showToast({
      title: '页面参数异常',
      icon: 'none'
    })
  }

  // 分类配置加载已在各自的设置方法中处理
  // 如果都没有处理到，则尝试直接加载
  if (!pageParams.value.from && pageParams.value.categoryId) {
    console.log('直接加载分类配置:', pageParams.value.categoryId)
    loadCategoryConfig(pageParams.value.categoryId)
  }
})

// 设置品牌信息（从index页面跳转）
const setBrandInfoFromIndex = () => {
  console.log('设置品牌信息（来自index）:', pageParams.value)

  brandInfo.value = {
    id: pageParams.value.brandId || '',
    name: pageParams.value.brandName || '未知品牌',
    image: pageParams.value.brandLogo || '',
    status: '已选择品牌'
  }

  console.log('品牌信息设置完成:', brandInfo.value)

  // 从缓存数据中获取分类ID
  if (pageParams.value.categoryId) {
    console.log('从缓存获取到分类ID:', pageParams.value.categoryId)
    loadCategoryConfig(pageParams.value.categoryId.toString())
  } else {
    console.warn('从index跳转但缺少分类ID')
  }
}

// 设置品牌和商品信息（从detail页面跳转）
const setBrandInfoFromDetail = () => {
  console.log('设置商品信息（来自detail）:', pageParams.value)

  // 设置商品信息
  productInfo.value = {
    id: pageParams.value.productId || '',
    name: pageParams.value.productName || '未知商品',
    image: pageParams.value.productImage || '',
    category_id: pageParams.value.categoryId || ''
  }

  // 设置品牌信息
  brandInfo.value = {
    id: pageParams.value.brandId || '',
    name: pageParams.value.brandName || '未知品牌',
    image: pageParams.value.brandLogo || '',
    status: '已选择商品'
  }

  console.log('商品信息设置完成:', productInfo.value)
  console.log('品牌信息设置完成:', brandInfo.value)

  // 从缓存数据中获取分类ID
  if (pageParams.value.categoryId) {
    console.log('从detail缓存获取到分类ID:', pageParams.value.categoryId)
    loadCategoryConfig(pageParams.value.categoryId.toString())
  } else {
    console.warn('从detail跳转但缺少分类ID')
  }
}
</script>

<style lang="scss" scoped>
.photo-evaluate-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding: 20rpx 0 120rpx;
}



// 品牌信息区域
.brand-section {
  position: relative;
  background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 50%, #ffffff 100%);
  padding: 32rpx;
  margin: 20rpx 32rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.1);

  .brand-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    position: relative;
    z-index: 2;

    .brand-logo {
      width: 80rpx;
      height: 80rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

      .logo-image {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .brand-details {
      flex: 1;

      .brand-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 8rpx;
      }

      .brand-status {
        font-size: 24rpx;
        color: #0d7377;
        display: block;
        font-weight: 500;
      }

      .product-name {
        font-size: 22rpx;
        color: #666;
        display: block;
        margin-top: 8rpx;
        font-weight: 400;
      }
    }

    .brand-placeholder {
      width: 100%;
      height: 100%;
      background: #f0f0f0;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-text {
        font-size: 20rpx;
        color: #999;
      }
    }
  }

  .brand-bg {
    position: absolute;
    top: -40rpx;
    right: -40rpx;
    width: 160rpx;
    height: 160rpx;
    z-index: 1;

    .bg-circle {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(13, 115, 119, 0.15) 0%, rgba(20, 160, 133, 0.1) 100%);
      border-radius: 50%;
    }
  }
}

// 区域标题样式
.section-header {
  margin-bottom: 24rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
  }

  .section-desc {
    font-size: 24rpx;
    color: #666;
    display: block;
  }
}

// 实物照片区域
.photo-section {
  background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);
  border: 1rpx solid rgba(13, 115, 119, 0.1);

  .photo-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .photo-item {
      width: calc((100% - 32rpx) / 3);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;

      .photo-placeholder {
        width: 100%;
        aspect-ratio: 1;
        background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border: 2rpx dashed rgba(13, 115, 119, 0.3);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        // 背景图片层
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: inherit;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          opacity: 0.6;
          z-index: 1;
        }

        &.uploaded {
          border-color: #0d7377;
          border-style: solid;
          box-shadow: 0 2rpx 12rpx rgba(13, 115, 119, 0.15);

          &::before {
            opacity: 0;
          }
        }

        &.uploading {
          border-color: #ffa726;
          border-style: solid;
          background: rgba(255, 167, 38, 0.05);
          box-shadow: 0 2rpx 12rpx rgba(255, 167, 38, 0.15);

          &::before {
            opacity: 0;
          }
        }

        &:active {
          transform: scale(0.98);
        }

        .placeholder-content {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 2;
          // 移除所有视觉样式，只保留点击区域
        }

        .photo-image {
          width: 100%;
          height: 100%;
          position: relative;
          z-index: 3;
        }
      }

      .photo-label {
        font-size: 24rpx;
        color: #333;
        text-align: center;
        font-weight: 500;
        margin-top: 4rpx;
        background: rgba(255, 255, 255, 0.9);
        padding: 4rpx 8rpx;
        border-radius: 8rpx;
        backdrop-filter: blur(2rpx);
      }
    }
  }

  // 加载状态
  .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;

    .empty-text {
      font-size: 26rpx;
      color: #999;
    }
  }
}

// 明显瑕疵区域
.defect-section {
  background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);
  border: 1rpx solid rgba(13, 115, 119, 0.1);

  .defect-photos {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .defect-item {
      width: calc((100% - 32rpx) / 3);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;

      .defect-placeholder {
        width: 100%;
        aspect-ratio: 1;
        background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
        border: 2rpx dashed rgba(13, 115, 119, 0.3);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        // 为新增按钮添加视觉提示
        &:not(.uploaded) {
          &::after {
            content: '+';
            font-size: 48rpx;
            color: rgba(13, 115, 119, 0.4);
            font-weight: 300;
          }
        }

        &.uploaded {
          border-color: #0d7377;
          border-style: solid;
          box-shadow: 0 2rpx 12rpx rgba(13, 115, 119, 0.15);
        }

        &.uploading {
          border-color: #ffa726;
          border-style: solid;
          background: rgba(255, 167, 38, 0.05);
          box-shadow: 0 2rpx 12rpx rgba(255, 167, 38, 0.15);
        }

        &:active {
          transform: scale(0.98);
        }

        .defect-image {
          width: 100%;
          height: 100%;
        }

        .delete-btn {
          position: absolute;
          top: 8rpx;
          right: 8rpx;
          width: 32rpx;
          height: 32rpx;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .defect-label {
        font-size: 22rpx;
        color: #666;
        text-align: center;
      }
    }
  }
}

// 配件区域
.accessories-section {
  background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);
  border: 1rpx solid rgba(13, 115, 119, 0.1);

  .accessories-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .accessory-item {
      padding: 16rpx 24rpx;
      background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
      border: 2rpx solid rgba(13, 115, 119, 0.2);
      border-radius: 24rpx;
      transition: all 0.3s ease;

      &.selected {
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-color: #0d7377;
        color: #fff;
        box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.3);
      }

      &:active {
        transform: scale(0.98);
      }

      .accessory-name {
        font-size: 26rpx;
        color: #0d7377;
        font-weight: 500;
      }

      &.selected .accessory-name {
        color: #fff;
        font-weight: 600;
      }
    }
  }
}

// 备注区域
.note-section {
  background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);
  border: 1rpx solid rgba(13, 115, 119, 0.1);

  .note-textarea {
    width: 100%;
    min-height: 120rpx;
    padding: 20rpx;
    background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
    border: 2rpx solid rgba(13, 115, 119, 0.2);
    border-radius: 16rpx;
    font-size: 26rpx;
    color: #333;
    line-height: 1.5;
    resize: none;
    box-sizing: border-box;
    word-wrap: break-word;
    word-break: break-all;

    &::placeholder {
      color: rgba(13, 115, 119, 0.6);
      font-size: 24rpx;
    }

    &:focus {
      border-color: #0d7377;
      background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
      box-shadow: 0 2rpx 12rpx rgba(13, 115, 119, 0.15);
    }
  }
}

// 底部提交按钮
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(240, 255, 254, 0.95) 0%, rgba(255, 255, 255, 0.98) 100%);
  backdrop-filter: blur(10rpx);
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(13, 115, 119, 0.1);
  z-index: 100;

  .submit-button {
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    color: #fff;
    padding: 18rpx 24rpx;
    border-radius: 50rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.25);
    position: relative;
    overflow: hidden;

    &.disabled {
      background: #e9ecef;
      color: #999;
      box-shadow: none;
    }

    // 光泽效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:active:not(.disabled) {
      transform: scale(0.98) translateY(1rpx);
      background: linear-gradient(135deg, #e55a2b 0%, #ff7a35 100%);
      box-shadow: 0 2rpx 12rpx rgba(255, 107, 53, 0.35);

      &::before {
        left: 100%;
      }
    }

    .submit-text {
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
      letter-spacing: 1rpx;
    }
  }
}

// 上传状态通用样式
.uploading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ffa726;

  .uploading-spinner {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid rgba(255, 167, 38, 0.2);
    border-top: 4rpx solid #ffa726;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8rpx;
  }

  .uploading-text {
    font-size: 24rpx;
    color: #ffa726;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
