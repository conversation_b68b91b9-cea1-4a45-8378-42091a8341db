<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\admin\express;

use addon\yz_she\app\model\express\ExpressLog;
use core\base\BaseAdminService;

/**
 * 物流回调日志服务层
 * Class ExpressLogService
 * @package addon\yz_she\app\service\admin\express
 */
class ExpressLogService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new ExpressLog();
    }

    /**
     * 获取物流日志列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id, order_id, waybill, shopbill, type, type_code, weight, real_weight, cal_weight, 
                  volume, total_freight, freight, courier_name, courier_phone, pickup_code, 
                  fee_over, create_time';
        
        $order = 'create_time desc';

        $search_model = $this->model
            ->withSearch(['waybill', 'shopbill', 'type_code', 'fee_over', 'courier_name', 'courier_phone', 'create_time'], $where)
            ->field($field)
            ->order($order)
            ->append(['type_code_text', 'fee_over_text', 'weight_text', 'real_weight_text', 'cal_weight_text', 'volume_text', 'total_freight_text', 'freight_text']);

        return $this->pageQuery($search_model);
    }

    /**
     * 获取物流日志详情
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id, order_id, waybill, shopbill, type, type_code, weight, real_weight, transfer_weight, 
                  cal_weight, volume, parse_weight, total_freight, freight, freight_insured, freight_haocai, 
                  change_bill, change_bill_freight, fee_over, courier_name, courier_phone, pickup_code, 
                  content, create_time';

        $info = $this->model->field($field)->where([['id', '=', $id]])->findOrEmpty()->toArray();
        if (empty($info)) {
            throw new \Exception('物流日志不存在');
        }

        // 格式化内容
        if (!empty($info['content'])) {
            $info['content_formatted'] = $this->formatContent($info['content']);
        }

        return $info;
    }

    /**
     * 删除物流日志
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $info = $this->model->findOrEmpty($id);
        if ($info->isEmpty()) {
            throw new \Exception('物流日志不存在');
        }

        $res = $info->delete();
        return $res !== false;
    }

    /**
     * 批量删除物流日志
     * @param array $ids
     * @return bool
     */
    public function batchDel(array $ids)
    {
        if (empty($ids)) {
            throw new \Exception('请选择要删除的数据');
        }

        $res = $this->model->where([['id', 'in', $ids]])->delete();
        return $res !== false;
    }

    /**
     * 导出物流日志
     * @param array $where
     * @return array
     */
    public function export(array $where = [])
    {
        $field = 'id, order_id, waybill, shopbill, type, type_code, weight, real_weight, cal_weight, 
                  volume, total_freight, freight, courier_name, courier_phone, pickup_code, 
                  fee_over, create_time';

        $list = $this->model
            ->withSearch(['waybill', 'shopbill', 'type_code', 'fee_over', 'courier_name', 'courier_phone', 'create_time'], $where)
            ->field($field)
            ->order('create_time desc')
            ->select()
            ->toArray();

        // 格式化数据
        foreach ($list as &$item) {
            $item['type_code_text'] = ExpressLog::getTypeCodeText($item['type_code']);
            $item['fee_over_text'] = ExpressLog::getFeeOverText($item['fee_over']);
            $item['weight_text'] = $item['weight'] > 0 ? $item['weight'] . 'kg' : '-';
            $item['real_weight_text'] = $item['real_weight'] > 0 ? $item['real_weight'] . 'kg' : '-';
            $item['cal_weight_text'] = $item['cal_weight'] > 0 ? $item['cal_weight'] . 'kg' : '-';
            $item['volume_text'] = $item['volume'] > 0 ? $item['volume'] . 'm³' : '-';
            $item['total_freight_text'] = $item['total_freight'] > 0 ? '¥' . number_format($item['total_freight'], 2) : '-';
            $item['freight_text'] = $item['freight'] > 0 ? '¥' . number_format($item['freight'], 2) : '-';
        }

        return [
            'header' => [
                'ID', '订单ID', '运单号', '商家单号', '状态描述', '状态码', '下单重量', '实际重量', 
                '计费重量', '体积', '总费用', '快递费', '快递员', '快递员电话', '取件码', 
                '扣费状态', '创建时间'
            ],
            'data' => $list
        ];
    }

    /**
     * 获取状态码选项
     * @return array
     */
    public function getTypeCodeOptions()
    {
        return ExpressLog::getTypeCodeOptions();
    }

    /**
     * 获取扣费状态选项
     * @return array
     */
    public function getFeeOverOptions()
    {
        return ExpressLog::getFeeOverOptions();
    }

    /**
     * 格式化回调内容
     * @param string $content
     * @return array
     */
    private function formatContent(string $content)
    {
        try {
            $data = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $data;
            }
        } catch (\Exception $e) {
            // JSON解析失败，返回原始内容
        }
        
        return ['raw_content' => $content];
    }

    /**
     * 获取统计数据
     * @param array $where
     * @return array
     */
    public function getStatistics(array $where = [])
    {
        // 基础查询条件
        $baseQuery = $this->model->withSearch(['create_time'], $where);

        $total = (clone $baseQuery)->count();
        $pending = (clone $baseQuery)->where('type_code', 1)->count(); // 待揽收
        $shipping = (clone $baseQuery)->where('type_code', 2)->count(); // 运输中
        $delivered = (clone $baseQuery)->where('type_code', 3)->count(); // 已签收
        $rejected = (clone $baseQuery)->where('type_code', 4)->count(); // 拒收退回
        $cancelled = (clone $baseQuery)->where('type_code', 99)->count(); // 已取消

        return [
            'total' => $total,
            'pending' => $pending,
            'shipping' => $shipping,
            'delivered' => $delivered,
            'rejected' => $rejected,
            'cancelled' => $cancelled
        ];
    }

    /**
     * 根据运单号获取最新日志
     * @param string $waybill
     * @return array
     */
    public function getLatestByWaybill(string $waybill)
    {
        $info = $this->model
            ->where('waybill', $waybill)
            ->order('create_time desc')
            ->findOrEmpty()
            ->toArray();

        return $info;
    }

    /**
     * 根据订单ID获取物流日志列表
     * @param int $orderId
     * @return array
     */
    public function getByOrderId(int $orderId)
    {
        $list = $this->model
            ->where('order_id', $orderId)
            ->order('create_time desc')
            ->select()
            ->toArray();

        return $list;
    }
}
