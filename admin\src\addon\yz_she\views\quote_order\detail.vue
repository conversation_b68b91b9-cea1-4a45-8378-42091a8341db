<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <!-- 页面头部 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <el-button @click="goBack" circle>
                        <template #icon>
                            <ArrowLeft />
                        </template>
                    </el-button>
                    <h2 class="ml-4 text-xl font-bold">估价订单详情</h2>
                </div>
                <div class="flex items-center space-x-2">
                    <el-button v-if="orderDetail.status === 1" type="success" @click="showQuoteDialog">估价</el-button>
                    <el-button v-if="[1, 2].includes(orderDetail.status)" type="danger" @click="cancelOrder">取消订单</el-button>
                    <el-button v-if="orderDetail.status === 3" type="primary" @click="completeOrder">完成订单</el-button>
                    <el-button @click="refreshDetail" circle>
                        <template #icon>
                            <RefreshRight />
                        </template>
                    </el-button>
                </div>
            </div>

            <div v-loading="loading">
                <div v-if="orderDetail.id">
                    <el-row :gutter="20">
                        <!-- 左侧内容 -->
                        <el-col :span="16">
                            <!-- 订单基本信息 -->
                            <el-card class="mb-4" shadow="never">
                                <template #header>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <el-icon class="mr-2"><Document /></el-icon>
                                            <span class="font-medium">订单信息</span>
                                        </div>
                                       
                                    </div>
                                </template>
                                <el-descriptions :column="2" border>
                                    <el-descriptions-item label="订单号">{{ orderDetail.order_no }}</el-descriptions-item>
                                    <el-descriptions-item label="订单状态">
                                        <el-tag :type="getStatusTagType(orderDetail.status)">{{ orderDetail.status_text }}</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="商品名称">{{ orderDetail.product_name }}</el-descriptions-item>
                                    <el-descriptions-item label="商品编码">{{ orderDetail.product_code || '无' }}</el-descriptions-item>
                                    <el-descriptions-item label="分类">{{ orderDetail.category_name || '无' }}</el-descriptions-item>
                                    <el-descriptions-item label="品牌">{{ orderDetail.brand_name || '无' }}</el-descriptions-item>
                                    <el-descriptions-item label="估价金额">
                                        <span class="text-lg font-bold text-green-600">{{ orderDetail.quote_price_text }}</span>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="成色评分" v-if="orderDetail.condition_score">
                                        <div class="flex items-center">
                                            <span class="mr-2">{{ orderDetail.condition_score }}/10</span>
                                            <el-rate v-model="orderDetail.condition_score" disabled :max="10" show-score />
                                        </div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="创建时间">{{ orderDetail.create_time_text }}</el-descriptions-item>
                                    <el-descriptions-item label="估价时间">{{ orderDetail.quote_time_text || '未估价' }}</el-descriptions-item>
                                    <el-descriptions-item label="确认时间">{{ orderDetail.confirm_time_text || '未确认' }}</el-descriptions-item>
                                    <el-descriptions-item label="发货时间">{{ orderDetail.ship_time_text || '未发货' }}</el-descriptions-item>
                                    <el-descriptions-item label="完成时间">{{ orderDetail.complete_time_text || '未完成' }}</el-descriptions-item>
                                    <el-descriptions-item label="取消时间" v-if="orderDetail.cancel_time_text">{{ orderDetail.cancel_time_text }}</el-descriptions-item>
                                    <el-descriptions-item label="估价说明" :span="2" v-if="orderDetail.quote_note">
                                        <div class="max-w-md">{{ orderDetail.quote_note }}</div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="管理员备注" :span="2" v-if="orderDetail.admin_note">
                                        <div class="max-w-md">{{ orderDetail.admin_note }}</div>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="取消原因" :span="2" v-if="orderDetail.cancel_reason">
                                        <div class="max-w-md text-red-600">{{ orderDetail.cancel_reason }}</div>
                                    </el-descriptions-item>
                                </el-descriptions>
                            </el-card>

                            <!-- 用户备注 -->
                            <el-card class="mb-4" shadow="never" v-if="orderDetail.user_note">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><ChatDotRound /></el-icon>
                                        <span class="font-medium">用户备注</span>
                                    </div>
                                </template>
                                <div class="p-2">
                                    <p class="text-gray-700">{{ orderDetail.user_note }}</p>
                                </div>
                            </el-card>

                            <!-- 实物照片 -->
                            <el-card class="mb-4" shadow="never" v-if="orderDetail.photos && orderDetail.photos.length">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><Camera /></el-icon>
                                        <span class="font-medium">实物照片</span>
                                        <el-tag size="small" class="ml-2">{{ orderDetail.photos.length }}张</el-tag>
                                    </div>
                                </template>
                                <div class="grid grid-cols-3 gap-4">
                                    <div v-for="photo in orderDetail.photos" :key="photo.id" class="photo-item">
                                        <el-image
                                            :src="img(photo.photo_url)"
                                            :preview-src-list="orderDetail.photos.map(p => img(p.photo_url))"
                                            class="w-full h-32 rounded-lg cursor-pointer border hover:shadow-md transition-shadow"
                                            :class="{ 'border-red-300': photo.is_defect }"
                                            fit="cover"
                                            :title="photo.photo_name || '实物照片'"
                                        >
                                            <template #error>
                                                <div class="w-full h-32 flex items-center justify-center bg-gray-100 rounded-lg">
                                                    <el-icon class="text-gray-400 text-2xl"><Picture /></el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                        <div class="photo-info mt-2">
                                            <div class="text-sm text-gray-600 text-center truncate" :title="photo.photo_name">
                                                {{ photo.photo_name || '实物照片' }}
                                            </div>
                                            <div class="flex justify-center mt-1">
                                                <el-tag 
                                                    :type="photo.is_defect ? 'danger' : 'success'" 
                                                    size="small"
                                                >
                                                    {{ photo.is_defect ? '瑕疵' : '正常' }}
                                                </el-tag>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 配件信息 -->
                            <el-card class="mb-4" shadow="never" v-if="orderDetail.accessories && orderDetail.accessories.length">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><Tools /></el-icon>
                                        <span class="font-medium">配件信息</span>
                                        <el-tag size="small" class="ml-2">{{ orderDetail.accessories.length }}项</el-tag>
                                    </div>
                                </template>
                                <div class="accessories-grid">
                                    <div v-for="accessory in orderDetail.accessories" :key="accessory.id" class="accessory-item">
                                        <div class="accessory-content">
                                            <div class="accessory-name">{{ accessory.accessory_name }}</div>
                                            <el-tag type="success" size="small">有</el-tag>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </el-col>

                        <!-- 右侧内容 -->
                        <el-col :span="8">
                            <!-- 用户信息 -->
                            <el-card class="mb-4" shadow="never" v-if="orderDetail.user_info">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><User /></el-icon>
                                        <span class="font-medium">用户信息</span>
                                    </div>
                                </template>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <el-avatar :src="img(orderDetail.user_info.headimg)" :size="50">
                                            {{ orderDetail.user_info.nickname?.charAt(0) || 'U' }}
                                        </el-avatar>
                                        <div class="ml-3">
                                            <div class="font-medium">{{ orderDetail.user_info.nickname || orderDetail.user_info.username || '未知用户' }}</div>
                                            <div class="text-sm text-gray-500">{{ orderDetail.user_info.mobile || '未绑定手机' }}</div>
                                        </div>
                                    </div>
                                    <el-divider class="my-3" />
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">用户ID:</span>
                                            <span>{{ orderDetail.user_id }}</span>
                                        </div>
                                        <div class="flex justify-between" v-if="orderDetail.user_info.real_name">
                                            <span class="text-gray-600">真实姓名:</span>
                                            <span>{{ orderDetail.user_info.real_name }}</span>
                                        </div>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 估价员信息 -->
                            <el-card class="mb-4" shadow="never" v-if="orderDetail.quote_records && orderDetail.quote_records.length > 0">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><UserFilled /></el-icon>
                                        <span class="font-medium">估价员信息</span>
                                    </div>
                                </template>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">估价员:</span>
                                        <span>{{ orderDetail.quote_records[0].admin_name || '系统' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">用户名:</span>
                                        <span>{{ orderDetail.quote_records[0].admin_info?.username || '-' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">估价时间:</span>
                                        <span>{{ orderDetail.quote_records[0].create_time_text || '未估价' }}</span>
                                    </div>
                                    <div class="flex justify-between" v-if="orderDetail.quote_records[0].quote_note">
                                        <span class="text-gray-600">估价说明:</span>
                                        <span class="text-right max-w-[200px]">{{ orderDetail.quote_records[0].quote_note }}</span>
                                    </div>
                                </div>
                            </el-card>

                            <!-- 订单状态流程 -->
                            <el-card shadow="never">
                                <template #header>
                                    <div class="flex items-center">
                                        <el-icon class="mr-2"><Clock /></el-icon>
                                        <span class="font-medium">订单流程</span>
                                    </div>
                                </template>
                                <el-timeline>
                                    <el-timeline-item
                                        v-if="orderDetail.create_time_text"
                                        timestamp="订单创建"
                                        :hollow="false"
                                        type="primary"
                                    >
                                        <div class="text-sm">{{ orderDetail.create_time_text }}</div>
                                    </el-timeline-item>
                                    <el-timeline-item
                                        v-if="orderDetail.quote_time_text"
                                        timestamp="完成估价"
                                        :hollow="false"
                                        type="success"
                                    >
                                        <div class="text-sm">{{ orderDetail.quote_time_text }}</div>
                                    </el-timeline-item>
                                    <el-timeline-item
                                        v-if="orderDetail.confirm_time_text"
                                        timestamp="用户确认"
                                        :hollow="false"
                                        type="success"
                                    >
                                        <div class="text-sm">{{ orderDetail.confirm_time_text }}</div>
                                    </el-timeline-item>
                                    <el-timeline-item
                                        v-if="orderDetail.ship_time_text"
                                        timestamp="订单发货"
                                        :hollow="false"
                                        type="success"
                                    >
                                        <div class="text-sm">{{ orderDetail.ship_time_text }}</div>
                                    </el-timeline-item>
                                    <el-timeline-item
                                        v-if="orderDetail.complete_time_text"
                                        timestamp="订单完成"
                                        :hollow="false"
                                        type="success"
                                    >
                                        <div class="text-sm">{{ orderDetail.complete_time_text }}</div>
                                    </el-timeline-item>
                                    <el-timeline-item
                                        v-if="orderDetail.cancel_time_text"
                                        timestamp="订单取消"
                                        :hollow="false"
                                        type="danger"
                                    >
                                        <div class="text-sm">{{ orderDetail.cancel_time_text }}</div>
                                        <div class="text-xs text-red-600 mt-1">{{ orderDetail.cancel_reason }}</div>
                                    </el-timeline-item>
                                </el-timeline>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </el-card>

        <!-- 估价对话框 -->
        <el-dialog v-model="quoteDialog.visible" title="商品估价" width="700px" :close-on-click-modal="false">
            <el-form :model="quoteDialog.form" :rules="quoteDialog.rules" label-width="100px" ref="quoteFormRef">
                <el-form-item label="估价金额" prop="quote_price">
                    <div class="flex items-center">
                        <el-input-number 
                            v-model="quoteDialog.form.quote_price" 
                            :min="0" 
                            :precision="2" 
                            placeholder="请输入估价金额"
                            class="w-[200px]" 
                        />
                        <span class="ml-2 text-gray-500">元</span>
                    </div>
                </el-form-item>
                
                <el-form-item label="成色评分" prop="condition_score">
                    <div class="w-full">
                        <!-- 评分滑块和输入框 -->
                        <div class="flex items-center space-x-4 mb-3">
                            <el-slider 
                                v-model="quoteDialog.form.condition_score" 
                                :min="1" 
                                :max="10" 
                                :step="0.5"
                                show-stops
                                class="flex-1 max-w-[400px]"
                                @input="onConditionScoreChange"
                            />
                            <el-input-number
                                v-model="quoteDialog.form.condition_score"
                                :min="1"
                                :max="10"
                                :step="0.5"
                                :precision="1"
                                class="w-[120px]"
                                @change="onConditionScoreChange"
                            />
                        </div>
                        
                        <!-- 成色显示 -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="text-lg font-bold text-blue-600">
                                    {{ Math.floor(quoteDialog.form.condition_score) }}成新
                                </div>
                                <div class="text-sm text-gray-500">
                                    ({{ quoteDialog.form.condition_score }}分)
                                </div>
                            </div>
                            <div class="text-sm font-medium text-green-600">
                                {{ getConditionText(quoteDialog.form.condition_score) }}
                            </div>
                        </div>

                    </div>
                </el-form-item>
                
                <el-form-item label="估价说明" prop="quote_note">
                    <el-input 
                        v-model="quoteDialog.form.quote_note" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请详细说明估价依据，如：成色状况、配件完整性、市场行情等"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="管理员备注" prop="admin_note">
                    <el-input 
                        v-model="quoteDialog.form.admin_note" 
                        type="textarea" 
                        :rows="2" 
                        placeholder="内部备注信息（用户不可见）"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="quoteDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="submitQuote" :loading="quoteDialog.loading">确定估价</el-button>
            </template>
        </el-dialog>

        <!-- 取消订单对话框 -->
        <el-dialog v-model="cancelDialog.visible" title="取消订单" width="500px" :close-on-click-modal="false">
            <el-form :model="cancelDialog.form" :rules="cancelDialog.rules" label-width="100px" ref="cancelFormRef">
                <el-form-item label="取消原因" prop="cancel_reason">
                    <el-input v-model="cancelDialog.form.cancel_reason" type="textarea" :rows="3" placeholder="请输入取消原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelDialog.visible = false">取消</el-button>
                <el-button type="danger" @click="submitCancel" :loading="cancelDialog.loading">确定取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
    ArrowLeft, 
    RefreshRight, 
    Document, 
    Camera, 
    Tools, 
    User, 
    UserFilled, 
    Clock,
    ChatDotRound,
    Edit,
    EditPen
} from '@element-plus/icons-vue'
import { img } from '@/utils/common'
import useUserStore from '@/stores/modules/user'
import { quoteOrderInfo, quoteOrderQuote, quoteOrderCancel, quoteOrderComplete } from '@/addon/yz_she/api/quote_order'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 订单详情数据
const orderDetail = ref<any>({})
const loading = ref(false)

// 估价对话框
const quoteDialog = reactive({
    visible: false,
    loading: false,
    form: {
        quote_price: 0,
        condition_score: 8,
        quote_note: '',
        admin_note: ''
    },
    rules: {
        quote_price: [
            { required: true, message: '请输入估价金额', trigger: 'blur' },
            { type: 'number', min: 0.01, message: '估价金额必须大于0', trigger: 'blur' }
        ],
        condition_score: [
            { required: true, message: '请设置成色评分', trigger: 'blur' },
            { type: 'number', min: 1, max: 10, message: '成色评分范围为1-10分', trigger: 'blur' }
        ]
    }
})

// 取消订单对话框
const cancelDialog = reactive({
    visible: false,
    loading: false,
    form: {
        cancel_reason: ''
    },
    rules: {
        cancel_reason: [
            { required: true, message: '请输入取消原因', trigger: 'blur' }
        ]
    }
})

const quoteFormRef = ref()
const cancelFormRef = ref()

// 获取订单详情
const getOrderDetail = async () => {
    try {
        loading.value = true
        const orderId = route.params.id as string
        const response = await quoteOrderInfo(parseInt(orderId))
        orderDetail.value = response.data // 修复：提取data字段中的实际数据
    } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败')
    } finally {
        loading.value = false
    }
}

// 返回列表页
const goBack = () => {
    router.back()
}

// 刷新详情
const refreshDetail = () => {
    getOrderDetail()
}

// 显示估价对话框
const showQuoteDialog = () => {
    quoteDialog.form = {
        quote_price: 0,
        condition_score: 8,
        quote_note: '',
        admin_note: ''
    }
    quoteDialog.visible = true
}

/**
 * 成色评分变化处理
 */
const onConditionScoreChange = (value: number) => {
    // 确保值在有效范围内
    if (value < 1) {
        quoteDialog.form.condition_score = 1
    } else if (value > 10) {
        quoteDialog.form.condition_score = 10
    }
}

// 提交估价
const submitQuote = () => {
    quoteFormRef.value.validate((valid: boolean) => {
        if (valid) {
            quoteDialog.loading = true
            
            // 获取当前管理员ID
            const adminId = userStore.userInfo?.uid || userStore.userInfo?.id || 0
            
            // 添加admin_id到请求参数
            const quoteData = {
                ...quoteDialog.form,
                admin_id: adminId
            }
            
            quoteOrderQuote(orderDetail.value.id, quoteData).then(() => {
                quoteDialog.loading = false
                quoteDialog.visible = false
                ElMessage.success('估价成功')
                getOrderDetail() // 刷新详情
            }).catch(() => {
                quoteDialog.loading = false
            })
        }
    })
}

// 取消订单
const cancelOrder = () => {
    cancelDialog.form.cancel_reason = ''
    cancelDialog.visible = true
}

// 提交取消
const submitCancel = async () => {
    try {
        await cancelFormRef.value?.validate()
        cancelDialog.loading = true
        
        await quoteOrderCancel(orderDetail.value.id, cancelDialog.form)
        
        ElMessage.success('订单已取消')
        cancelDialog.visible = false
        await getOrderDetail() // 刷新详情
    } catch (error) {
        console.error('取消订单失败:', error)
        ElMessage.error('取消订单失败')
    } finally {
        cancelDialog.loading = false
    }
}

// 完成订单
const completeOrder = async () => {
    try {
        await ElMessageBox.confirm('确定要完成此订单吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
        
        await quoteOrderComplete(orderDetail.value.id)
        ElMessage.success('订单已完成')
        await getOrderDetail() // 刷新详情
    } catch (error) {
        if (error !== 'cancel') {
            console.error('完成订单失败:', error)
            ElMessage.error('完成订单失败')
        }
    }
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
    const statusMap: Record<number, string> = {
        1: 'warning',  // 估价中
        2: 'primary',  // 待确认
        3: 'info',     // 待发货
        4: 'success',  // 已完成
        5: 'danger'    // 已取消
    }
    return statusMap[status] || 'info'
}

// 获取成色文本 - 与list页面保持一致
const getConditionText = (score: number) => {
    if (score >= 10) return '全新'
    if (score >= 9.5) return '九五新'
    if (score >= 9) return '九成新'
    if (score >= 8.5) return '八五新'
    if (score >= 8) return '八成新'
    if (score >= 7.5) return '七五新'
    if (score >= 7) return '七成新'
    if (score >= 6.5) return '六五新'
    if (score >= 6) return '六成新'
    if (score >= 5.5) return '五五新'
    if (score >= 5) return '五成新'
    return '五成新以下'
}

onMounted(() => {
    getOrderDetail()
})
</script>

<style scoped>
.main-container {
    padding: 20px;
}

.el-descriptions :deep(.el-descriptions__label) {
    font-weight: 500;
}

.el-timeline :deep(.el-timeline-item__timestamp) {
    font-weight: 500;
    color: #606266;
}

/* 品牌logo样式 */
.brand-logo {
    display: flex;
    align-items: center;
    opacity: 0.7;
}

/* 实物照片样式 */
.photo-item {
    display: flex;
    flex-direction: column;
}

.photo-info {
    padding: 8px 4px;
}

.photo-info .text-sm {
    line-height: 1.4;
    max-height: 2.8em;
    overflow: hidden;
}

/* 配件信息网格样式 */
.accessories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
}

.accessory-item {
    padding: 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.accessory-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.accessory-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accessory-name {
    flex: 1;
    font-weight: 500;
    color: #374151;
    margin-right: 8px;
}
</style>
