<template>
  <view class="brand-page">
    <!-- 顶部搜索栏 -->
    <view class="search-section">
      <view class="search-container">
        <view class="search-box">
          <view class="search-icon">
            <svg viewBox="0 0 1024 1024" width="20" height="20">
              <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" fill="currentColor"/>
            </svg>
          </view>
          <input
            class="search-input"
            type="text"
            placeholder="输入品牌名搜索"
            v-model="searchKeyword"
            @input="onSearchInput"
            @confirm="onSearch"
          />
        </view>
        <view class="search-button" @click="onSearch">搜索</view>
      </view>
    </view>

    <!-- 分类选择区域 -->
    <view class="category-section">
      <view class="category-tabs">
        <view
          class="category-tab"
          v-for="category in categories"
          :key="category.id"
          :class="{ active: selectedCategory === category.id }"
          @click="selectCategory(category)"
        >
          <text class="tab-title">{{ category.name }}</text>
          <view class="tab-underline" v-if="selectedCategory === category.id"></view>
        </view>
      </view>
    </view>

    <!-- 品牌快捷入口区 -->
    <view class="hot-brands-section" v-show="!isSearching">
      <view class="section-header">
        <text class="section-title">热门品牌</text>
      </view>
      <view class="brands-grid">
        <view
          class="brand-card"
          v-for="brand in hotBrands"
          :key="brand.id"
          @click="selectBrand(brand)"
        >
          <view class="brand-logo-container">
            <image
              v-if="brand.logo"
              :src="img(brand.logo)"
              class="brand-logo"
              mode="aspectFill"
            />
            <view v-else class="brand-logo-placeholder">
              <text class="brand-initial">{{ (brand.hot_name || brand.name).charAt(0) }}</text>
            </view>
          </view>
          <text class="brand-name">{{ brand.hot_name || brand.name }}</text>
        </view>
      </view>
    </view>

    <!-- 热门回收展示区 -->
    <view class="hot-recycle-section" v-show="!isSearching">
      <view class="section-header">
        <text class="section-title">热门回收</text>
      </view>
      <view class="recycle-grid">
        <view
          class="recycle-item"
          v-for="item in hotRecycleItems.slice(0, 4)"
          :key="item.id"
          @click="viewRecycleItem(item)"
        >
          <view class="item-image">
            <image
              v-if="item.image"
              :src="img(item.image)"
              class="product-image"
              mode="aspectFill"
            />
            <view v-else class="image-placeholder"></view>
          </view>
          <view class="item-info">
            <text class="item-name">{{ truncateText(item.name, 15) }}</text>
            <text class="item-price">
              <text class="price-label">最高</text>
              <text class="price-value"> ¥{{ item.price }}</text>
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 辅助功能区 -->
    <view class="helper-section" v-show="!isSearching">
      <view class="helper-content">
        <view class="help-info" @click="showHelp">
          <text class="help-text">线上估价太麻烦？</text>
          <view class="help-badge">
            <text class="help-icon">?</text>
          </view>
        </view>
        <view class="batch-btn" @click="batchOrder">
          <text class="batch-text">批量下单</text>
        </view>
      </view>
    </view>

    <!-- 自定义帮助弹窗 -->
    <view class="custom-modal" v-if="showHelpModal" @click="closeHelp">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">批量下单</text>
        </view>
        <view class="modal-body">
          <text class="modal-text">您可批量创建回收订单进行上门取件回收，无需逐个添加商品，仅需填写数量。鉴定仓库收到货后会为您进行商品清点并且拍照进行报价。</text>
        </view>
        <view class="modal-footer">
          <view class="modal-btn" @click="closeHelp">
            <text class="modal-btn-text">了解了</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 全部品牌列表区 -->
    <view class="all-brands-section">
      <view class="section-header">
        <text class="section-title">全部品牌</text>
      </view>

      <!-- 字母索引 -->
      <view class="alphabet-sidebar">
        <view
          class="alphabet-letter"
          v-for="letter in availableLetters"
          :key="letter"
          :class="{ active: currentLetter === letter }"
          @click="scrollToLetter(letter)"
        >
          {{ letter }}
        </view>
      </view>

      <!-- 品牌列表 -->
      <view class="brands-container">
        <view
          v-for="group in brandGroups"
          :key="group.letter"
          :id="`letter-${group.letter}`"
          class="brand-group"
        >
          <view class="group-title">{{ group.letter }}</view>
          <view
            class="brand-row"
            v-for="brand in group.brands"
            :key="brand.id"
            @click="selectBrand(brand)"
          >
            <view class="brand-avatar">
              <image
                v-if="brand.logo"
                :src="img(brand.logo)"
                class="avatar-image"
                mode="aspectFill"
              />
              <text v-else class="avatar-text">{{ brand.name.charAt(0) }}</text>
            </view>
            <text class="brand-label">{{ brand.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getCategoryList, getHotBrands, getAllBrands, searchBrands, getHotRecycleItems } from '@/addon/yz_she/api/brand'
import { img } from '@/utils/common'
import useMemberStore from '@/stores/member'
import { useLogin } from '@/hooks/useLogin'

// 响应式数据
const memberStore = useMemberStore()
const userInfo = computed(() => memberStore.info)
const searchKeyword = ref('')
const currentLetter = ref('A')
const loading = ref(false)
const selectedCategory = ref(1)
const showHelpModal = ref(false)
const isSearching = ref(false)

// 分类数据
const categories = ref([])


// 热门品牌数据
const hotBrands = ref([])

// 热门回收商品数据
const hotRecycleItems = ref([])

// 字母索引
const alphabetList = ref(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '其他'])

// 全部品牌数据
const allBrands = ref([])
// 原始品牌数据（用于搜索后恢复）
const originalBrands = ref([])

// 计算属性：按字母分组的品牌
const brandGroups = computed(() => {
  const groups: { [key: string]: any[] } = {}

  allBrands.value.forEach(brand => {
    const letter = brand.letter
    if (!groups[letter]) {
      groups[letter] = []
    }
    groups[letter].push(brand)
  })

  return alphabetList.value.map(letter => ({
    letter,
    brands: groups[letter] || []
  })).filter(group => group.brands.length > 0)
})

// 计算属性：存在的字母索引
const availableLetters = computed(() => {
  const letters = new Set<string>()
  allBrands.value.forEach(brand => {
    letters.add(brand.letter)
  })
  return alphabetList.value.filter(letter => letters.has(letter))
})

// 方法
const onSearchInput = () => {
  // 实时搜索处理（可以添加防抖）
  if (searchKeyword.value.trim()) {
    isSearching.value = true
    performSearch()
  } else {
    // 搜索框为空时，恢复原始数据
    isSearching.value = false
    restoreOriginalBrands()
  }
}

const onSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 搜索框为空时，恢复原始数据
    isSearching.value = false
    restoreOriginalBrands()
    return
  }
  isSearching.value = true
  performSearch()
}

const performSearch = async () => {
  try {
    loading.value = true
    const result = await searchBrands(searchKeyword.value, { category_id: selectedCategory.value })
    if (result.code === 1) {
      // 更新搜索结果到全部品牌列表
      allBrands.value = result.data || []
      uni.showToast({
        title: `找到${result.data.length}个品牌`,
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 计算属性
const selectedCategoryName = computed(() => {
  const category = categories.value.find(cat => cat.id === selectedCategory.value)
  return category ? category.name : '请选择分类'
})

const selectCategory = async (category: any) => {
  selectedCategory.value = category.id
  console.log('选择分类:', category)
  // 重新加载该分类下的数据
  await Promise.all([
    loadHotBrands(),
    loadAllBrands(),
    loadHotRecycleItems()
  ])
}

const selectBrand = (brand: any) => {
  console.log('选择品牌:', brand)

  // 将品牌详细信息存储到本地缓存
  uni.setStorageSync('selectedBrandInfo', {
    id: brand.id,
    name: brand.name,
    logo: brand.logo,
    hot_name: brand.hot_name,
    category_id: brand.category_id
  })

  // 跳转到免费评估页面，只传递品牌ID
  uni.navigateTo({
    url: `/addon/yz_she/pages/evaluate/index?brandId=${brand.id}`
  })
}

// 文本截断函数
const truncateText = (text: string, maxLength: number) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const viewRecycleItem = (item: any) => {
  console.log('查看回收商品:', item)
  // 跳转到商品详情页
  uni.navigateTo({
    url: `/addon/yz_she/pages/product/detail?id=${item.id}`
  })
}

// 恢复原始品牌数据
const restoreOriginalBrands = () => {
  if (originalBrands.value.length > 0) {
    allBrands.value = [...originalBrands.value]
  }
  isSearching.value = false
}

const showHelp = () => {
  showHelpModal.value = true
}

const closeHelp = () => {
  showHelpModal.value = false
}

const batchOrder = () => {
  console.log('批量下单')

  // 检查是否登录
  if (!userInfo.value) {
    console.log('用户未登录，跳转到登录页面')
    useLogin().setLoginBack({
      url: '/addon/yz_she/pages/brand/index'
    })
    return false
  }

  // 跳转到批量下单页面
  uni.navigateTo({
    url: '/addon/yz_she/pages/evaluate/batch'
  })
}

const scrollToLetter = (letter: string) => {
  currentLetter.value = letter
  // 使用页面滚动到指定元素
  uni.pageScrollTo({
    selector: `#letter-${letter}`,
    duration: 300
  })
}

// 加载数据的方法
const loadCategories = async () => {
  try {
    const result = await getCategoryList()
    if (result.code === 1) {
      categories.value = result.data || []
      // 默认选择第一个分类
      if (categories.value.length > 0) {
        selectedCategory.value = categories.value[0].id
      }
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadHotBrands = async () => {
  try {
    const result = await getHotBrands({ category_id: selectedCategory.value })
    if (result.code === 1) {
      hotBrands.value = result.data || []
    }
  } catch (error) {
    console.error('加载热门品牌失败:', error)
  }
}

const loadAllBrands = async () => {
  try {
    const result = await getAllBrands({ category_id: selectedCategory.value })
    if (result.code === 1) {
      allBrands.value = result.data || []
      // 保存原始数据
      originalBrands.value = [...(result.data || [])]
    }
  } catch (error) {
    console.error('加载全部品牌失败:', error)
  }
}

const loadHotRecycleItems = async () => {
  try {
    // 获取当前分类下的热门商品，限制4个
    const params = {
      category_id: selectedCategory.value,
      is_hot: 1,
      status: 1,
      limit: 4
    }

    const result = await getHotRecycleItems(params)
    if (result.code === 1) {
      // API返回的是分页数据结构 {data: {data: [...], total: ...}}
      const items = result.data.data || []
      hotRecycleItems.value = items.map(item => ({
        id: item.id,
        name: item.name,
        image: item.image,
        price: Math.max(item.price_new || 0, item.price_used || 0, item.price_damaged || 0)
      }))
    }
  } catch (error) {
    console.error('加载热门回收商品失败:', error)
  }
}

onMounted(async () => {
  // 页面加载完成后的初始化逻辑
  await loadCategories()
  await Promise.all([
    loadHotBrands(),
    loadAllBrands(),
    loadHotRecycleItems()
  ])
})
</script>

<style lang="scss" scoped>
.brand-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 搜索区域
.search-section {
  background-color: #fff;
  padding: 20rpx 24rpx 0;
  margin: 0;
  border-radius: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);


  .search-container {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 50rpx;
      padding: 0 24rpx;
      height: 80rpx;
      border: 1rpx solid #e8e8e8;
      transition: all 0.3s ease;

      &:focus-within {
        border-color: #333;
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .search-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        color: #333;

        svg {
          width: 32rpx;
          height: 32rpx;

          path {
            stroke-width: 2;
            font-weight: bold;
          }
        }
      }

      .search-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
        color: #333;
        background: transparent;

        &::placeholder {
          color: #999;
        }
      }
    }

    .search-button {
      background-color: transparent;
      color: #333;
      padding: 8rpx 20rpx;
      border-radius: 40rpx;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.7;
        transform: scale(0.95);
      }
    }
  }
}

// 分类选择区域
.category-section {
  background-color: #fff;
  margin: 0;
  padding: 0 0 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .category-tabs {
    display: flex;
    align-items: center;

    .category-tab {
      flex: 1;
      position: relative;
      padding: 20rpx 12rpx 8rpx;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.7;
      }

      .tab-title {
        font-size: 32rpx;
        color: #666;
        font-weight: 500;
        transition: all 0.3s ease;
        display: block;
      }

      .tab-underline {
        position: absolute;
        bottom: 4rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background: linear-gradient(90deg, #16a085, #1abc9c);
        box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.4);
        border-radius: 2rpx;
      }

      &.active {
        .tab-title {
          color: #333;
          font-weight: 600;
          text-shadow: none;
        }
      }
    }
  }
}

// 通用区域样式
.section-header {
  padding: 30rpx 24rpx 20rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
  }
}

// 热门品牌区域
.hot-brands-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .brands-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;
    padding: 0 24rpx 30rpx;

    .brand-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx 12rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:active {
        background-color: #f8f9fa;
        transform: scale(0.95);
      }

      .brand-logo-container {
        margin-bottom: 12rpx;

        .brand-logo {
          width: 88rpx;
          height: 88rpx;
          border-radius: 20rpx;
          background-color: #fff;
          border: 1rpx solid #ddd;
          padding: 8rpx;
          box-sizing: border-box;
        }

        .brand-logo-placeholder {
          width: 88rpx;
          height: 88rpx;
          background-color: #f5f5f5;
          border-radius: 5rpx;
          border: 1rpx solid #ddd;
          padding: 8rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;

          .brand-initial {
            font-size: 28rpx;
            font-weight: 600;
            color: #495057;
          }
        }
      }

      .brand-name {
        font-size: 26rpx;
        color: #333;
        text-align: center;
        font-weight: 500;
      }
    }
  }
}

// 热门回收区域
.hot-recycle-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .recycle-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
    padding: 0 24rpx 24rpx;

    .recycle-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 12rpx;
      background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
      border-radius: 10rpx;
      box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
      border: 1rpx solid #f0f2f5;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
        background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
      }

      .item-image {
        width: 70rpx;
        height: 70rpx;
        border-radius: 8rpx;
        overflow: hidden;
        margin-right: 16rpx;
        flex-shrink: 0;
        background-color: #fff;
        box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);

        .product-image {
          width: 100%;
          height: 100%;
        }

        .image-placeholder {
          width: 100%;
          height: 100%;
          background-color: #e0e0e0;
          border-radius: 8rpx;
        }
      }

      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 6rpx;
        padding: 0;

        .item-name {
          font-size: 24rpx;
          color: #2c3e50;
          font-weight: 600;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 200rpx;
          margin: 0;
        }

        .item-price {
          display: flex;
          align-items: baseline;
          font-family: 'PingFang SC', -apple-system, sans-serif;

          .price-label {
            color: #7f8c8d;
            font-size: 18rpx;
            font-weight: 400;
            margin-right: 4rpx;
          }

          .price-value {
            color: #e74c3c;
            font-size: 22rpx;
            font-weight: 700;
          }
        }
      }
    }
  }
}

// 辅助功能区域
.helper-section {
  margin: 20rpx 24rpx;

  .helper-content {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

    .help-info {
      display: flex;
      align-items: center;
      gap: 12rpx;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.7;
      }

      .help-text {
        font-size: 28rpx;
        color: #495057;
        font-weight: 600;
      }

      .help-badge {
        width: 34rpx;
        height: 34rpx;
        background-color: transparent;
        border: 2rpx solid #ccc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .help-icon {
          font-size: 20rpx;
          color: #999;
          font-weight: 600;
        }

        &:hover {
          border-color: #999;
          transform: scale(1.05);
        }
      }
    }

    .batch-btn {
      background-color: #333;
      color: #fff;
      padding: 12rpx 24rpx;
      border-radius: 25rpx;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(51, 51, 51, 0.3);
      margin-left: -5rpx;

      &:active {
        transform: scale(0.95);
        background-color: #555;
        box-shadow: 0 2rpx 8rpx rgba(51, 51, 51, 0.4);
      }

      .batch-text {
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }
}

// 全部品牌区域
.all-brands-section {
  background-color: #fff;
  margin: 20rpx 24rpx 40rpx;
  border-radius: 16rpx;
  position: relative;
  min-height: 400rpx;

  .alphabet-sidebar {
    position: fixed;
    right: 12rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 12rpx 8rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(15rpx);
    border: 1rpx solid rgba(0, 0, 0, 0.05);

    .alphabet-letter {
      width: 40rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      color: var(--text-color-light6, #666);
      font-weight: 500;
      margin-bottom: 4rpx;
      border-radius: 8rpx;
      transition: all 0.3s ease;

      &.active {
        color: #1abc9c;
        background-color: rgba(26, 188, 156, 0.2);
        transform: scale(1.1);
        font-weight: 700;
        box-shadow: 0 2rpx 8rpx rgba(26, 188, 156, 0.3);
      }

      &:active {
        transform: scale(0.95);
        opacity: 0.7;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .brands-container {
    padding: 0 0 20rpx;

    .brand-group {
      &:not(:first-child) {
        border-top: 1rpx solid #f0f0f0;
      }

      .group-title {
        font-size: 26rpx;
        font-weight: 600;
        color: #666;
        padding: 24rpx 24rpx 16rpx;
        background-color: #fff;
      }

      .brand-row {
        display: flex;
        align-items: center;
        padding: 20rpx 24rpx;
        border-bottom: 1rpx solid #f8f9fa;
        transition: all 0.3s ease;
        background-color: #fff;

        &:active {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .brand-avatar {
          width: 110rpx;
          height: 110rpx;
          background-color: #f0f0f0;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          overflow: hidden;

          .avatar-image {
            width: 100%;
            height: 100%;
          }

          .avatar-text {
            font-size: 36rpx;
            font-weight: 600;
            color: #666;
          }
        }

        .brand-label {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}

// 自定义弹窗样式
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 40rpx;
    max-width: 600rpx;
    width: 100%;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);

    .modal-header {
      padding: 32rpx 32rpx 16rpx;
      text-align: center;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .modal-body {
      padding: 24rpx 32rpx;

      .modal-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        text-align: left;
      }
    }

    .modal-footer {
      padding: 16rpx 32rpx 32rpx;
      display: flex;
      justify-content: center;

      .modal-btn {
        background-color: #f5f5f5;
        border-radius: 8rpx;
        padding: 16rpx 32rpx;
        min-width: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          background-color: #e8e8e8;
          transform: scale(0.98);
        }

        .modal-btn-text {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}


</style>
