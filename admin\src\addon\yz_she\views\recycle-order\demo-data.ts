/**
 * 回收订单演示数据
 */

// 演示订单数据
export const demoOrders = [
    // 估价订单
    {
        id: 1,
        order_no: 'RC2024120001',
        source_type: 1,
        source_type_text: '估价订单确认',
        status: 3,
        status_text: '待质检',
        settlement_status: 0,
        settlement_status_text: '未结算',
        member_id: 1001,
        quote_order_id: 2001,
        category_id: 1,
        brand_id: 1,
        product_id: null,
        product_name: '小米13 Ultra 512GB 黑色',
        product_code: null,
        product_image: null,
        quantity: 1,
        expected_price: 3500.00,
        final_price: 3200.00,
        voucher_amount: 200.00,
        total_amount: 3400.00,
        delivery_type: 1,
        delivery_type_text: '快递上门',
        pickup_contact_name: '张三',
        pickup_contact_phone: '13800138001',
        pickup_address_detail: '北京市朝阳区建国路88号SOHO现代城A座1001室',
        pickup_time: '2024-01-15 09:00-12:00',
        pickup_time_actual_text: '2024-01-14 10:30:00',
        express_company: 'SF',
        express_number: 'SF1234567890001',
        receive_time_text: '2024-01-15 14:20:00',
        quality_score: 85,
        quality_note: '外观良好，功能正常，电池健康度85%',
        create_time_text: '2024-01-14 08:33:44',
        member: {
            member_id: 1001,
            nickname: '张三',
            mobile: '13800138001',
            headimg: 'https://via.placeholder.com/60x60'
        },
        brand: {
            id: 1,
            name: '小米',
            logo: 'https://via.placeholder.com/50x50/007bff/ffffff?text=MI'
        },
        category: {
            id: 1,
            name: '手机数码'
        },
        quote_order: {
            id: 2001,
            order_no: 'QT2024120001',
            photos_count: 8
        }
    },
    
    // 直接回收订单
    {
        id: 2,
        order_no: 'RC2024120002',
        source_type: 2,
        source_type_text: '直接回收',
        status: 2,
        status_text: '待收货',
        settlement_status: 0,
        settlement_status_text: '未结算',
        member_id: 1002,
        quote_order_id: null,
        category_id: 2,
        brand_id: 2,
        product_id: 1001,
        product_name: '联想ThinkPad X1 Carbon 2023款',
        product_code: 'TPX1C2023',
        product_image: 'https://via.placeholder.com/200x200/28a745/ffffff?text=ThinkPad',
        quantity: 1,
        expected_price: 8500.00,
        final_price: null,
        voucher_amount: 0.00,
        total_amount: null,
        delivery_type: 1,
        delivery_type_text: '快递上门',
        pickup_contact_name: '李四',
        pickup_contact_phone: '13800138002',
        pickup_address_detail: '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼',
        pickup_time: '2024-01-15 14:00-18:00',
        pickup_time_actual_text: '2024-01-15 15:45:00',
        express_company: 'YTO',
        express_number: 'YT9876543210002',
        receive_time_text: null,
        quality_score: null,
        quality_note: null,
        create_time_text: '2024-01-15 10:22:15',
        member: {
            member_id: 1002,
            nickname: '李四',
            mobile: '13800138002',
            headimg: 'https://via.placeholder.com/60x60'
        },
        brand: {
            id: 2,
            name: '联想',
            logo: 'https://via.placeholder.com/50x50/dc3545/ffffff?text=Lenovo'
        },
        category: {
            id: 2,
            name: '笔记本电脑'
        }
    },
    
    // 批量下单
    {
        id: 3,
        order_no: 'RC2024120003',
        source_type: 3,
        source_type_text: '批量下单',
        status: 1,
        status_text: '待取件',
        settlement_status: 0,
        settlement_status_text: '未结算',
        member_id: 1003,
        quote_order_id: null,
        category_id: 1,
        brand_id: null,
        product_id: null,
        product_name: null,
        product_code: null,
        product_image: null,
        quantity: 5,
        expected_price: 0.00,
        final_price: null,
        voucher_amount: 0.00,
        total_amount: null,
        delivery_type: 2,
        delivery_type_text: '自行寄出',
        pickup_contact_name: '王五',
        pickup_contact_phone: '13800138003',
        pickup_address_detail: '广州市天河区珠江新城花城大道85号高德置地广场A座',
        pickup_time: null,
        pickup_time_actual_text: null,
        express_company: null,
        express_number: null,
        receive_time_text: null,
        quality_score: null,
        quality_note: null,
        create_time_text: '2024-01-15 16:45:30',
        member: {
            member_id: 1003,
            nickname: '王五',
            mobile: '13800138003',
            headimg: 'https://via.placeholder.com/60x60'
        },
        brand: null,
        category: {
            id: 1,
            name: '手机数码'
        }
    },
    
    // 已完成的估价订单
    {
        id: 4,
        order_no: 'RC2024120004',
        source_type: 1,
        source_type_text: '估价订单确认',
        status: 7,
        status_text: '已完成',
        settlement_status: 1,
        settlement_status_text: '已结算',
        member_id: 1004,
        quote_order_id: 2002,
        category_id: 1,
        brand_id: 3,
        product_id: null,
        product_name: 'iPhone 12 128GB 白色',
        product_code: null,
        product_image: null,
        quantity: 1,
        expected_price: 3500.00,
        final_price: 3300.00,
        voucher_amount: 100.00,
        total_amount: 3400.00,
        delivery_type: 1,
        delivery_type_text: '快递上门',
        pickup_contact_name: '赵六',
        pickup_contact_phone: '13800138004',
        pickup_address_detail: '深圳市南山区科技园南区深南大道9988号',
        pickup_time: '2024-01-10 09:00-12:00',
        pickup_time_actual_text: '2024-01-10 10:15:00',
        express_company: 'SF',
        express_number: 'SF1234567890004',
        receive_time_text: '2024-01-11 09:30:00',
        quality_start_time_text: '2024-01-11 10:00:00',
        quality_complete_time_text: '2024-01-11 15:30:00',
        confirm_time_text: '2024-01-11 18:20:00',
        settlement_time_text: '2024-01-12 09:00:00',
        quality_score: 92,
        quality_note: '外观完好，功能正常，电池健康度92%，无维修记录',
        create_time_text: '2024-01-10 08:15:22',
        member: {
            member_id: 1004,
            nickname: '赵六',
            mobile: '13800138004',
            headimg: 'https://via.placeholder.com/60x60'
        },
        brand: {
            id: 3,
            name: '苹果',
            logo: 'https://via.placeholder.com/50x50/000000/ffffff?text=Apple'
        },
        category: {
            id: 1,
            name: '手机数码'
        },
        quote_order: {
            id: 2002,
            order_no: 'QT2024120002',
            photos_count: 6
        }
    }
]

// 演示地址数据
export const demoAddresses = [
    {
        id: 1,
        member_id: 1001,
        name: '张三',
        mobile: '13800138001',
        province_id: 110000,
        city_id: 110100,
        district_id: 110105,
        address: '建国路88号SOHO现代城A座1001室',
        full_address: '北京市北京市朝阳区建国路88号SOHO现代城A座1001室',
        is_default: 1
    },
    {
        id: 2,
        member_id: 1002,
        name: '李四',
        mobile: '13800138002',
        province_id: 310000,
        city_id: 310100,
        district_id: 310115,
        address: '陆家嘴环路1000号恒生银行大厦20楼',
        full_address: '上海市上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼',
        is_default: 1
    }
]

// 演示估价订单照片数据
export const demoQuotePhotos = [
    {
        id: 1,
        quote_order_id: 2001,
        photo_url: 'https://via.placeholder.com/300x300/007bff/ffffff?text=正面',
        photo_type: 1,
        is_defect: 0,
        sort: 1
    },
    {
        id: 2,
        quote_order_id: 2001,
        photo_url: 'https://via.placeholder.com/300x300/007bff/ffffff?text=背面',
        photo_type: 1,
        is_defect: 0,
        sort: 2
    },
    {
        id: 3,
        quote_order_id: 2001,
        photo_url: 'https://via.placeholder.com/300x300/007bff/ffffff?text=侧面',
        photo_type: 1,
        is_defect: 0,
        sort: 3
    },
    {
        id: 4,
        quote_order_id: 2001,
        photo_url: 'https://via.placeholder.com/300x300/dc3545/ffffff?text=划痕',
        photo_type: 2,
        is_defect: 1,
        sort: 4
    }
]

// 演示物流信息
export const demoLogistics = {
    SF1234567890001: {
        express_company: 'SF',
        express_company_name: '顺丰速运',
        express_number: 'SF1234567890001',
        status: 3,
        status_text: '已签收',
        last_update_time: '2024-01-15 14:30:00',
        traces: [
            {
                time: '2024-01-15 14:30:00',
                status: '已签收',
                context: '您的快件已签收，感谢使用顺丰速运',
                location: '北京市朝阳区'
            },
            {
                time: '2024-01-15 09:15:00',
                status: '派件中',
                context: '快件正在派送中，派送员：张师傅，联系电话：138****1234',
                location: '北京市朝阳区营业点'
            },
            {
                time: '2024-01-15 06:20:00',
                status: '到达目的地',
                context: '快件已到达北京市朝阳区营业点',
                location: '北京市朝阳区营业点'
            },
            {
                time: '2024-01-14 22:45:00',
                status: '运输中',
                context: '快件正在运输途中',
                location: '北京转运中心'
            },
            {
                time: '2024-01-14 18:30:00',
                status: '已揽收',
                context: '快件已被顺丰速运揽收',
                location: '上海市浦东新区营业点'
            }
        ]
    },
    YT9876543210002: {
        express_company: 'YTO',
        express_company_name: '圆通速递',
        express_number: 'YT9876543210002',
        status: 2,
        status_text: '运输中',
        last_update_time: '2024-01-15 20:45:00',
        traces: [
            {
                time: '2024-01-15 20:45:00',
                status: '运输中',
                context: '快件正在运输途中',
                location: '北京转运中心'
            },
            {
                time: '2024-01-15 15:45:00',
                status: '已揽收',
                context: '快件已被圆通速递揽收',
                location: '上海市浦东新区营业点'
            }
        ]
    }
}

// 快递公司列表
export const expressCompanies = [
    { code: 'SF', name: '顺丰速运', logo: 'https://via.placeholder.com/30x30/007bff/ffffff?text=SF' },
    { code: 'YTO', name: '圆通速递', logo: 'https://via.placeholder.com/30x30/28a745/ffffff?text=YTO' },
    { code: 'ZTO', name: '中通快递', logo: 'https://via.placeholder.com/30x30/dc3545/ffffff?text=ZTO' },
    { code: 'STO', name: '申通快递', logo: 'https://via.placeholder.com/30x30/ffc107/ffffff?text=STO' },
    { code: 'EMS', name: '中国邮政', logo: 'https://via.placeholder.com/30x30/17a2b8/ffffff?text=EMS' }
]

// 获取演示订单数据
export const getDemoOrder = (id: number) => {
    return demoOrders.find(order => order.id === id)
}

// 获取演示物流数据
export const getDemoLogistics = (expressNumber: string) => {
    return demoLogistics[expressNumber as keyof typeof demoLogistics]
}

// 获取演示估价照片
export const getDemoQuotePhotos = (quoteOrderId: number) => {
    return demoQuotePhotos.filter(photo => photo.quote_order_id === quoteOrderId)
}
