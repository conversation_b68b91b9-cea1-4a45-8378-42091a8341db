<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\admin\recycle;

use addon\yz_she\app\model\recycle\RecycleStandard;
use core\base\BaseAdminService;
use core\exception\AdminException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 回收标准服务类
 * Class RecycleStandardService
 * @package addon\yz_she\app\service\admin\recycle
 */
class RecycleStandardService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new RecycleStandard();
    }

    /**
     * 获取回收标准列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = []): array
    {
        $field = 'id,category_id,title,image,description,sort,status,create_time,update_time';
        $order = 'sort desc,id desc';

        $search_model = $this->model->field($field)
            ->with([
                'category' => function($query) {
                    $query->field('id,name');
                }
            ])
            ->order($order);

        // 搜索条件
        if (!empty($where['category_id'])) {
            $search_model->where('category_id', $where['category_id']);
        }

        if (!empty($where['title'])) {
            $search_model->where('title', 'like', '%' . $where['title'] . '%');
        }

        if (isset($where['status']) && $where['status'] !== '') {
            $search_model->where('status', $where['status']);
        }

        $list = $this->pageQuery($search_model);

        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
            $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['update_time_text'] = date('Y-m-d H:i:s', $item['update_time']);
        }

        return $list;
    }

    /**
     * 获取回收标准详情
     * @param int $id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getInfo(int $id): array
    {
        $field = 'id,category_id,title,image,description,sort,status,create_time,update_time';

        $info = $this->model->field($field)
            ->with([
                'category' => function($query) {
                    $query->field('id,name');
                }
            ])
            ->where('id', $id)
            ->findOrFail()
            ->toArray();

        // 格式化数据
        $info['status_text'] = $info['status'] == 1 ? '启用' : '禁用';
        $info['create_time_text'] = date('Y-m-d H:i:s', $info['create_time']);
        $info['update_time_text'] = date('Y-m-d H:i:s', $info['update_time']);

        return $info;
    }

    /**
     * 添加回收标准
     * @param array $data
     * @return int
     */
    public function add(array $data): int
    {
        $data['create_time'] = time();
        $data['update_time'] = time();

        $result = $this->model->create($data);
        return $result->id;
    }

    /**
     * 编辑回收标准
     * @param int $id
     * @param array $data
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function edit(int $id, array $data): bool
    {
        $this->model->findOrFail($id);
        $data['update_time'] = time();

        $this->model->where('id', $id)->update($data);
        return true;
    }

    /**
     * 删除回收标准
     * @param int $id
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function del(int $id): bool
    {
        $model = $this->model->findOrFail($id);
        $model->delete();
        return true;
    }

    /**
     * 修改状态
     * @param int $id
     * @param int $status
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function modifyStatus(int $id, int $status): bool
    {
        $this->model->findOrFail($id);
        $this->model->where('id', $id)->update([
            'status' => $status,
            'update_time' => time()
        ]);
        return true;
    }

    /**
     * 批量操作
     * @param array $data
     * @return bool
     */
    public function batch(array $data): bool
    {
        if (empty($data['ids']) || empty($data['action'])) {
            throw new AdminException('参数错误');
        }

        switch ($data['action']) {
            case 'delete':
                $this->model->whereIn('id', $data['ids'])->delete();
                break;
            case 'enable':
                $this->model->whereIn('id', $data['ids'])->update([
                    'status' => 1,
                    'update_time' => time()
                ]);
                break;
            case 'disable':
                $this->model->whereIn('id', $data['ids'])->update([
                    'status' => 0,
                    'update_time' => time()
                ]);
                break;
            default:
                throw new AdminException('不支持的操作');
        }

        return true;
    }
}