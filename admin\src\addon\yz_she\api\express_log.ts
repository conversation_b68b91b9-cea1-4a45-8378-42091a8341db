import request from '@/utils/request'

/**
 * 获取物流日志列表
 */
export function getExpressLogList(params: Record<string, any>) {
    return request.get(`yz_she/express_log/lists`, { params })
}

/**
 * 获取物流日志详情
 */
export function getExpressLogInfo(id: number) {
    return request.get(`yz_she/express_log/info/${id}`)
}

/**
 * 删除物流日志
 */
export function deleteExpressLog(id: number) {
    return request.delete(`yz_she/express_log/${id}`)
}

/**
 * 批量删除物流日志
 */
export function batchDeleteExpressLog(ids: number[]) {
    return request.delete(`yz_she/express_log/batch`, { ids })
}

/**
 * 导出物流日志
 */
export function exportExpressLog(params: Record<string, any>) {
    return request.get(`yz_she/express_log/export`, { params })
}

/**
 * 获取状态码选项
 */
export function getTypeCodeOptions() {
    return request.get(`yz_she/express_log/get_type_code_options`)
}

/**
 * 获取扣费状态选项
 */
export function getFeeOverOptions() {
    return request.get(`yz_she/express_log/get_fee_over_options`)
}

/**
 * 获取统计数据
 */
export function getExpressLogStatistics(params: Record<string, any>) {
    return request.get(`yz_she/express_log/get_statistics`, { params })
}

/**
 * 根据运单号获取最新日志
 */
export function getLatestByWaybill(waybill: string) {
    return request.get(`yz_she/express_log/get_latest_by_waybill`, { params: { waybill } })
}

/**
 * 根据订单ID获取物流日志
 */
export function getByOrderId(orderId: number) {
    return request.get(`yz_she/express_log/get_by_order_id`, { params: { order_id: orderId } })
}
