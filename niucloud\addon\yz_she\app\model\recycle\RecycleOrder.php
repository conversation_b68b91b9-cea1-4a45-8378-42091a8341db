<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\recycle;

use addon\yz_she\app\model\goods\Brand;
use addon\yz_she\app\model\goods\Category;
use addon\yz_she\app\model\order\QuoteOrder;
use addon\yz_she\app\model\voucher\Voucher;
use app\model\member\Member;
use app\model\member\MemberAddress;
use app\model\sys\SysUser;
use core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 回收订单模型
 * Class RecycleOrder
 * @package addon\yz_she\app\model\recycle
 */
class RecycleOrder extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_recycle_orders';

    /**
     * 订单状态
     * @var array
     */
    public static $status = [
        1 => '待取件',
        2 => '待收货', 
        3 => '待质检',
        4 => '待确认',
        5 => '待退回',
        6 => '已退回',
        7 => '已完成'
    ];

    /**
     * 订单来源
     * @var array
     */
    public static $sourceType = [
        1 => '估价订单确认',
        2 => '直接回收',
        3 => '批量下单'
    ];

    /**
     * 配送方式
     * @var array
     */
    public static $deliveryType = [
        1 => '快递上门',
        2 => '自行寄出'
    ];

    /**
     * 结算方式
     * @var array
     */
    public static $settlementType = [
        1 => '余额结算'
    ];

    /**
     * 结算状态
     * @var array
     */
    public static $settlementStatus = [
        0 => '未结算',
        1 => '已结算'
    ];

    /**
     * 关联会员
     * @return BelongsTo
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'member_id');
    }

    /**
     * 关联分类
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 关联品牌
     * @return BelongsTo
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brand_id', 'id');
    }

    /**
     * 关联估价订单
     * @return BelongsTo
     */
    public function quoteOrder(): BelongsTo
    {
        return $this->belongsTo(QuoteOrder::class, 'quote_order_id', 'id');
    }

    /**
     * 关联加价券
     * @return BelongsTo
     */
    public function voucher(): BelongsTo
    {
        return $this->belongsTo(Voucher::class, 'voucher_id', 'id');
    }

    /**
     * 关联取件地址
     * @return BelongsTo
     */
    public function pickupAddress(): BelongsTo
    {
        return $this->belongsTo(MemberAddress::class, 'pickup_address_id', 'id');
    }

    /**
     * 关联质检员
     * @return BelongsTo
     */
    public function qualityAdmin(): BelongsTo
    {
        return $this->belongsTo(SysUser::class, 'quality_check_admin_id', 'uid');
    }

    /**
     * 关联结算操作员
     * @return BelongsTo
     */
    public function settlementAdmin(): BelongsTo
    {
        return $this->belongsTo(SysUser::class, 'settlement_admin_id', 'uid');
    }

    /**
     * 获取状态列表
     * @return array
     */
    public static function getStatus(): array
    {
        return self::$status;
    }

    /**
     * 获取订单来源列表
     * @return array
     */
    public static function getSourceType(): array
    {
        return self::$sourceType;
    }

    /**
     * 获取配送方式列表
     * @return array
     */
    public static function getDeliveryType(): array
    {
        return self::$deliveryType;
    }

    /**
     * 获取结算方式列表
     * @return array
     */
    public static function getSettlementType(): array
    {
        return self::$settlementType;
    }

    /**
     * 获取结算状态列表
     * @return array
     */
    public static function getSettlementStatus(): array
    {
        return self::$settlementStatus;
    }

    /**
     * 状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data): string
    {
        return self::$status[$data['status']] ?? '';
    }

    /**
     * 订单来源文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getSourceTypeTextAttr($value, $data): string
    {
        return self::$sourceType[$data['source_type']] ?? '';
    }

    /**
     * 配送方式文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getDeliveryTypeTextAttr($value, $data): string
    {
        return self::$deliveryType[$data['delivery_type']] ?? '';
    }

    /**
     * 结算方式文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getSettlementTypeTextAttr($value, $data): string
    {
        return self::$settlementType[$data['settlement_type']] ?? '';
    }

    /**
     * 结算状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getSettlementStatusTextAttr($value, $data): string
    {
        return self::$settlementStatus[$data['settlement_status']] ?? '';
    }

    /**
     * 质检图片获取器
     * @param $value
     * @return array
     */
    public function getQualityImagesAttr($value): array
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 质检图片设置器
     * @param $value
     * @return string
     */
    public function setQualityImagesAttr($value): string
    {
        return is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 生成订单编号
     * @return string
     */
    public static function generateOrderNo(): string
    {
        return 'XC' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 搜索器：订单编号
     * @param $query
     * @param $value
     */
    public function searchOrderNoAttr($query, $value)
    {
        if ($value) {
            $query->where('order_no', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：会员ID
     * @param $query
     * @param $value
     */
    public function searchMemberIdAttr($query, $value)
    {
        if ($value) {
            $query->where('member_id', $value);
        }
    }

    /**
     * 搜索器：状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：订单来源
     * @param $query
     * @param $value
     */
    public function searchSourceTypeAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('source_type', $value);
        }
    }

    /**
     * 搜索器：创建时间
     * @param $query
     * @param $value
     */
    public function searchCreateTimeAttr($query, $value)
    {
        if ($value && is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }
}
