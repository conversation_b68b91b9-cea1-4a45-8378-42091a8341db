<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\express;

use core\base\BaseModel;

/**
 * 物流回调日志模型
 * Class ExpressLog
 * @package addon\yz_she\app\model\express
 */
class ExpressLog extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_express_log';

    /**
     * 自动写入时间戳字段
     * @var bool
     */
    protected $autoWriteTimestamp = 'datetime';

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = false;

    /**
     * 状态码映射
     * @var array
     */
    public static $typeCodeMap = [
        1 => '待揽收',
        2 => '运输中',
        3 => '已签收',
        4 => '拒收退回',
        99 => '已取消'
    ];

    /**
     * 扣费状态映射
     * @var array
     */
    public static $feeOverMap = [
        0 => '冻结',
        1 => '已扣费'
    ];

    /**
     * 获取状态码文本
     * @param int $typeCode
     * @return string
     */
    public static function getTypeCodeText($typeCode)
    {
        return self::$typeCodeMap[$typeCode] ?? '未知状态';
    }

    /**
     * 获取扣费状态文本
     * @param int $feeOver
     * @return string
     */
    public static function getFeeOverText($feeOver)
    {
        return self::$feeOverMap[$feeOver] ?? '未知';
    }

    /**
     * 获取状态码选项
     * @return array
     */
    public static function getTypeCodeOptions()
    {
        $options = [];
        foreach (self::$typeCodeMap as $value => $label) {
            $options[] = [
                'value' => $value,
                'label' => $label
            ];
        }
        return $options;
    }

    /**
     * 获取扣费状态选项
     * @return array
     */
    public static function getFeeOverOptions()
    {
        $options = [];
        foreach (self::$feeOverMap as $value => $label) {
            $options[] = [
                'value' => $value,
                'label' => $label
            ];
        }
        return $options;
    }

    /**
     * 搜索器：运单号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchWaybillAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('waybill', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：商家单号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchShopbillAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('shopbill', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：状态码
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchTypeCodeAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('type_code', $value);
        }
    }

    /**
     * 搜索器：扣费状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchFeeOverAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('fee_over', $value);
        }
    }

    /**
     * 搜索器：快递员姓名
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCourierNameAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('courier_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：快递员电话
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCourierPhoneAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('courier_phone', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：创建时间范围
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCreateTimeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            if (is_array($value) && count($value) == 2) {
                $query->whereBetweenTime('create_time', $value[0], $value[1]);
            }
        }
    }

    /**
     * 获取器：状态码文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeCodeTextAttr($value, $data)
    {
        return self::getTypeCodeText($data['type_code'] ?? 0);
    }

    /**
     * 获取器：扣费状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getFeeOverTextAttr($value, $data)
    {
        return self::getFeeOverText($data['fee_over'] ?? 0);
    }

    /**
     * 获取器：格式化重量
     * @param $value
     * @param $data
     * @return string
     */
    public function getWeightTextAttr($value, $data)
    {
        $weight = $data['weight'] ?? 0;
        return $weight > 0 ? $weight . 'kg' : '-';
    }

    /**
     * 获取器：格式化实际重量
     * @param $value
     * @param $data
     * @return string
     */
    public function getRealWeightTextAttr($value, $data)
    {
        $weight = $data['real_weight'] ?? 0;
        return $weight > 0 ? $weight . 'kg' : '-';
    }

    /**
     * 获取器：格式化计费重量
     * @param $value
     * @param $data
     * @return string
     */
    public function getCalWeightTextAttr($value, $data)
    {
        $weight = $data['cal_weight'] ?? 0;
        return $weight > 0 ? $weight . 'kg' : '-';
    }

    /**
     * 获取器：格式化体积
     * @param $value
     * @param $data
     * @return string
     */
    public function getVolumeTextAttr($value, $data)
    {
        $volume = $data['volume'] ?? 0;
        return $volume > 0 ? $volume . 'm³' : '-';
    }

    /**
     * 获取器：格式化费用
     * @param $value
     * @param $data
     * @return string
     */
    public function getTotalFreightTextAttr($value, $data)
    {
        $freight = $data['total_freight'] ?? 0;
        return $freight > 0 ? '¥' . number_format($freight, 2) : '-';
    }

    /**
     * 获取器：格式化快递费
     * @param $value
     * @param $data
     * @return string
     */
    public function getFreightTextAttr($value, $data)
    {
        $freight = $data['freight'] ?? 0;
        return $freight > 0 ? '¥' . number_format($freight, 2) : '-';
    }

    /**
     * 关联回收订单
     * @return \think\model\relation\BelongsTo
     */
    public function recycleOrder()
    {
        return $this->belongsTo('addon\yz_she\app\model\recycle\RecycleOrder', 'order_id', 'id');
    }
}
