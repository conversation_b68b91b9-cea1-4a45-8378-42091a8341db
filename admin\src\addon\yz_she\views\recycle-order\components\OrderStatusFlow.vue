<template>
    <div class="order-status-flow">
        <div class="flow-header mb-4">
            <h3 class="text-lg font-medium">订单流程状态</h3>
            <p class="text-gray-500 text-sm">当前状态：{{ orderInfo.status_text }}</p>
        </div>

        <!-- 流程步骤 -->
        <div class="flow-steps">
            <el-steps :active="currentStep" :process-status="processStatus" align-center>
                <el-step
                    v-for="(step, index) in flowSteps"
                    :key="index"
                    :title="step.title"
                    :description="step.description"
                    :icon="step.icon"
                    :status="step.status"
                >
                    <template #description>
                        <div class="step-description">
                            <p>{{ step.description }}</p>
                            <p v-if="step.time" class="text-xs text-gray-500 mt-1">{{ step.time }}</p>
                            <div v-if="step.actions && step.actions.length > 0" class="step-actions mt-2">
                                <el-button
                                    v-for="action in step.actions"
                                    :key="action.key"
                                    :type="action.type"
                                    size="small"
                                    @click="handleAction(action)"
                                >
                                    {{ action.label }}
                                </el-button>
                            </div>
                        </div>
                    </template>
                </el-step>
            </el-steps>
        </div>

        <!-- 状态详情卡片 -->
        <div class="status-cards mt-6">
            <el-row :gutter="16">
                <el-col :span="8" v-for="card in statusCards" :key="card.key">
                    <el-card class="status-card" :class="card.class">
                        <div class="flex items-center">
                            <el-icon :class="card.iconClass" class="text-2xl mr-3">
                                <component :is="card.icon" />
                            </el-icon>
                            <div>
                                <h4 class="font-medium">{{ card.title }}</h4>
                                <p class="text-sm text-gray-600">{{ card.description }}</p>
                                <p v-if="card.time" class="text-xs text-gray-500 mt-1">{{ card.time }}</p>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions mt-6" v-if="quickActions.length > 0">
            <h4 class="text-sm font-medium mb-3">快捷操作</h4>
            <div class="flex flex-wrap gap-2">
                <el-button
                    v-for="action in quickActions"
                    :key="action.key"
                    :type="action.type"
                    :icon="action.icon"
                    @click="handleAction(action)"
                >
                    {{ action.label }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import {
    Clock,
    Van,
    DocumentChecked,
    Search,
    CircleCheck,
    RefreshLeft,
    Money,
    Warning,
    InfoFilled
} from '@element-plus/icons-vue'

interface OrderInfo {
    id: number
    status: number
    status_text: string
    source_type: number
    create_time_text: string
    pickup_time_actual_text?: string
    receive_time_text?: string
    quality_start_time_text?: string
    quality_complete_time_text?: string
    confirm_time_text?: string
    return_time_text?: string
    settlement_time_text?: string
    settlement_status: number
}

interface FlowStep {
    title: string
    description: string
    icon: string
    status: string
    time?: string
    actions?: Array<{
        key: string
        label: string
        type: string
        icon?: string
    }>
}

const props = defineProps<{
    orderInfo: OrderInfo
}>()

const emit = defineEmits<{
    action: [actionKey: string, orderInfo: OrderInfo]
}>()

// 当前步骤
const currentStep = computed(() => {
    const status = props.orderInfo.status
    if (status <= 1) return 0  // 待取件
    if (status <= 2) return 1  // 待收货
    if (status <= 3) return 2  // 待质检
    if (status <= 4) return 3  // 待确认
    if (status === 5) return 3  // 待退回（停留在待确认步骤）
    if (status === 6) return 4  // 已退回
    if (status === 7) return 4  // 已完成
    return 0
})

// 流程状态
const processStatus = computed(() => {
    const status = props.orderInfo.status
    if (status === 5 || status === 6) return 'error'  // 退回状态
    if (status === 7) return 'success'  // 完成状态
    return 'process'  // 进行中
})

// 流程步骤
const flowSteps = computed<FlowStep[]>(() => {
    const info = props.orderInfo
    const steps: FlowStep[] = [
        {
            title: '待取件',
            description: '等待快递员上门取件',
            icon: 'Clock',
            status: info.status >= 1 ? 'finish' : 'wait',
            time: info.create_time_text,
            actions: info.status === 1 ? [
                { key: 'update_express', label: '更新快递', type: 'primary' }
            ] : []
        },
        {
            title: '待收货',
            description: '商品运输中，等待仓库收货',
            icon: 'Van',
            status: info.status >= 2 ? 'finish' : 'wait',
            time: info.pickup_time_actual_text,
            actions: info.status === 2 ? [
                { key: 'receive_order', label: '收货确认', type: 'primary' }
            ] : []
        },
        {
            title: '待质检',
            description: '商品已到达，等待质检',
            icon: 'Search',
            status: info.status >= 3 ? 'finish' : 'wait',
            time: info.receive_time_text,
            actions: info.status === 3 ? [
                { key: 'start_quality', label: '开始质检', type: 'primary' }
            ] : []
        },
        {
            title: '待确认',
            description: '质检完成，等待用户确认价格',
            icon: 'DocumentChecked',
            status: info.status >= 4 ? 'finish' : 'wait',
            time: info.quality_complete_time_text,
            actions: info.status === 4 ? [
                { key: 'complete_quality', label: '完成质检', type: 'primary' }
            ] : []
        },
        {
            title: info.status === 6 ? '已退回' : '已完成',
            description: info.status === 6 ? '商品已退回给用户' : '订单完成，等待结算',
            icon: info.status === 6 ? 'RefreshLeft' : 'CircleCheck',
            status: (info.status === 6 || info.status === 7) ? 'finish' : 'wait',
            time: info.status === 6 ? info.return_time_text : info.settlement_time_text,
            actions: (info.status === 7 && info.settlement_status === 0) ? [
                { key: 'settlement', label: '结算订单', type: 'success' }
            ] : []
        }
    ]

    return steps
})

// 状态卡片
const statusCards = computed(() => {
    const info = props.orderInfo
    const cards = []

    // 当前状态卡片
    cards.push({
        key: 'current_status',
        title: '当前状态',
        description: info.status_text,
        time: '',
        icon: getStatusIcon(info.status),
        iconClass: getStatusIconClass(info.status),
        class: getStatusCardClass(info.status)
    })

    // 订单来源卡片
    cards.push({
        key: 'order_source',
        title: '订单来源',
        description: getSourceTypeText(info.source_type),
        time: info.create_time_text,
        icon: 'InfoFilled',
        iconClass: 'text-blue-500',
        class: 'border-blue-200'
    })

    // 结算状态卡片
    if (info.status === 7) {
        cards.push({
            key: 'settlement_status',
            title: '结算状态',
            description: info.settlement_status === 1 ? '已结算' : '待结算',
            time: info.settlement_time_text || '',
            icon: 'Money',
            iconClass: info.settlement_status === 1 ? 'text-green-500' : 'text-orange-500',
            class: info.settlement_status === 1 ? 'border-green-200' : 'border-orange-200'
        })
    }

    return cards
})

// 快捷操作
const quickActions = computed(() => {
    const info = props.orderInfo
    const actions = []

    if (info.status === 2) {
        actions.push({ key: 'receive_order', label: '收货确认', type: 'primary', icon: 'DocumentChecked' })
    }
    if (info.status === 3) {
        actions.push({ key: 'start_quality', label: '开始质检', type: 'primary', icon: 'Search' })
    }
    if (info.status === 4) {
        actions.push({ key: 'complete_quality', label: '完成质检', type: 'primary', icon: 'CircleCheck' })
    }
    if (info.status === 7 && info.settlement_status === 0) {
        actions.push({ key: 'settlement', label: '结算订单', type: 'success', icon: 'Money' })
    }

    return actions
})

/**
 * 获取状态图标
 */
const getStatusIcon = (status: number) => {
    const icons: Record<number, string> = {
        1: 'Clock',
        2: 'Van',
        3: 'Search',
        4: 'DocumentChecked',
        5: 'Warning',
        6: 'RefreshLeft',
        7: 'CircleCheck'
    }
    return icons[status] || 'InfoFilled'
}

/**
 * 获取状态图标样式
 */
const getStatusIconClass = (status: number) => {
    const classes: Record<number, string> = {
        1: 'text-orange-500',
        2: 'text-blue-500',
        3: 'text-purple-500',
        4: 'text-yellow-500',
        5: 'text-red-500',
        6: 'text-gray-500',
        7: 'text-green-500'
    }
    return classes[status] || 'text-gray-500'
}

/**
 * 获取状态卡片样式
 */
const getStatusCardClass = (status: number) => {
    const classes: Record<number, string> = {
        1: 'border-orange-200',
        2: 'border-blue-200',
        3: 'border-purple-200',
        4: 'border-yellow-200',
        5: 'border-red-200',
        6: 'border-gray-200',
        7: 'border-green-200'
    }
    return classes[status] || 'border-gray-200'
}

/**
 * 获取订单来源文本
 */
const getSourceTypeText = (sourceType: number) => {
    const texts: Record<number, string> = {
        1: '估价订单确认',
        2: '直接回收',
        3: '批量下单'
    }
    return texts[sourceType] || '未知来源'
}

/**
 * 处理操作
 */
const handleAction = (action: any) => {
    emit('action', action.key, props.orderInfo)
}
</script>

<style lang="scss" scoped>
.order-status-flow {
    .flow-steps {
        :deep(.el-step__description) {
            margin-top: 8px;
        }
    }

    .status-card {
        border: 2px solid;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    }

    .step-description {
        text-align: center;
    }

    .step-actions {
        display: flex;
        justify-content: center;
        gap: 8px;
    }

    .quick-actions {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
    }
}
</style>
