<template>
    <div class="main-container">
        <!-- 页面头部 -->
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">物流回调日志</span>
                <div class="flex items-center space-x-2">
                    <el-button type="primary" @click="refreshData">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button @click="exportData" :loading="exportLoading">
                        <el-icon><Download /></el-icon>
                        导出
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-4">
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-blue-100 text-blue-600">
                        <el-icon><Van /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.total }}</div>
                        <div class="text-gray-500 text-sm">总数</div>
                    </div>
                </div>
            </el-card>
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-orange-100 text-orange-600">
                        <el-icon><Clock /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.pending }}</div>
                        <div class="text-gray-500 text-sm">待揽收</div>
                    </div>
                </div>
            </el-card>
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-blue-100 text-blue-600">
                        <el-icon><Van /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.shipping }}</div>
                        <div class="text-gray-500 text-sm">运输中</div>
                    </div>
                </div>
            </el-card>
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-green-100 text-green-600">
                        <el-icon><CircleCheck /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.delivered }}</div>
                        <div class="text-gray-500 text-sm">已签收</div>
                    </div>
                </div>
            </el-card>
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-red-100 text-red-600">
                        <el-icon><CloseBold /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.rejected }}</div>
                        <div class="text-gray-500 text-sm">拒收退回</div>
                    </div>
                </div>
            </el-card>
            <el-card class="stat-card" shadow="hover">
                <div class="flex items-center">
                    <div class="stat-icon bg-gray-100 text-gray-600">
                        <el-icon><Remove /></el-icon>
                    </div>
                    <div class="ml-3">
                        <div class="text-2xl font-bold">{{ statistics.cancelled }}</div>
                        <div class="text-gray-500 text-sm">已取消</div>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 搜索筛选 -->
        <el-card class="box-card !border-none mt-4" shadow="never">
            <el-form :model="expressLogTable.searchParam" label-width="90px" :inline="true">
                <el-form-item label="运单号">
                    <el-input v-model="expressLogTable.searchParam.waybill" placeholder="请输入运单号" clearable class="w-[200px]" />
                </el-form-item>
                <el-form-item label="商家单号">
                    <el-input v-model="expressLogTable.searchParam.shopbill" placeholder="请输入商家单号" clearable class="w-[200px]" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="expressLogTable.searchParam.type_code" placeholder="请选择状态" clearable class="w-[150px]">
                        <el-option v-for="item in typeCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣费状态">
                    <el-select v-model="expressLogTable.searchParam.fee_over" placeholder="请选择扣费状态" clearable class="w-[150px]">
                        <el-option v-for="item in feeOverOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="快递员">
                    <el-input v-model="expressLogTable.searchParam.courier_name" placeholder="请输入快递员姓名" clearable class="w-[150px]" />
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                        v-model="expressLogTable.searchParam.create_time"
                        type="datetimerange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        class="w-[300px]"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="loadExpressLogList()">搜索</el-button>
                    <el-button @click="resetForm">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 数据表格 -->
        <el-card class="box-card !border-none mt-4" shadow="never">
            <div class="flex justify-between items-center mb-4">
                <span class="text-sm text-gray-600">共 {{ expressLogTable.total }} 条数据</span>
                <div class="flex items-center space-x-2">
                    <el-button 
                        type="danger" 
                        :disabled="!expressLogTable.selectIds.length" 
                        @click="batchDelete"
                    >
                        <el-icon><Delete /></el-icon>
                        批量删除
                    </el-button>
                </div>
            </div>

            <el-table
                :data="expressLogTable.data"
                size="large"
                v-loading="expressLogTable.loading"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" />
                
                <!-- 运单信息 -->
                <el-table-column label="运单信息" min-width="200">
                    <template #default="{ row }">
                        <div class="flex flex-col">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm font-medium">{{ row.waybill }}</span>
                                <el-button type="primary" link size="small" @click="copyText(row.waybill)">
                                    <el-icon><CopyDocument /></el-icon>
                                </el-button>
                            </div>
                            <div class="text-xs text-gray-500" v-if="row.shopbill">
                                商家单号：{{ row.shopbill }}
                            </div>
                            <div class="text-xs text-gray-500">
                                订单ID：{{ row.order_id }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 状态信息 -->
                <el-table-column label="状态信息" min-width="150">
                    <template #default="{ row }">
                        <div class="flex flex-col">
                            <el-tag :type="getStatusTagType(row.type_code)" size="small" class="mb-1">
                                {{ row.type_code_text }}
                            </el-tag>
                            <div class="text-xs text-gray-600" v-if="row.type">
                                {{ row.type }}
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 重量体积 -->
                <el-table-column label="重量/体积" min-width="120">
                    <template #default="{ row }">
                        <div class="flex flex-col text-xs">
                            <div v-if="row.weight > 0">下单：{{ row.weight_text }}</div>
                            <div v-if="row.real_weight > 0">实际：{{ row.real_weight_text }}</div>
                            <div v-if="row.cal_weight > 0">计费：{{ row.cal_weight_text }}</div>
                            <div v-if="row.volume > 0" class="text-gray-500">体积：{{ row.volume_text }}</div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 费用信息 -->
                <el-table-column label="费用信息" min-width="120">
                    <template #default="{ row }">
                        <div class="flex flex-col text-xs">
                            <div v-if="row.total_freight > 0" class="font-medium">
                                总费用：{{ row.total_freight_text }}
                            </div>
                            <div v-if="row.freight > 0">
                                快递费：{{ row.freight_text }}
                            </div>
                            <el-tag 
                                :type="row.fee_over == 1 ? 'success' : 'warning'" 
                                size="small" 
                                class="mt-1"
                            >
                                {{ row.fee_over_text }}
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>

                <!-- 快递员信息 -->
                <el-table-column label="快递员" min-width="120">
                    <template #default="{ row }">
                        <div class="flex flex-col text-xs" v-if="row.courier_name">
                            <div class="font-medium">{{ row.courier_name }}</div>
                            <div class="text-gray-500" v-if="row.courier_phone">{{ row.courier_phone }}</div>
                            <div class="text-gray-500" v-if="row.pickup_code">取件码：{{ row.pickup_code }}</div>
                        </div>
                        <span v-else class="text-gray-400">-</span>
                    </template>
                </el-table-column>

                <!-- 创建时间 -->
                <el-table-column label="创建时间" width="160">
                    <template #default="{ row }">
                        <div class="text-xs">{{ row.create_time }}</div>
                    </template>
                </el-table-column>

                <!-- 操作 -->
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <div class="flex flex-col space-y-1">
                            <el-button type="primary" link size="small" @click="viewDetail(row.id)">
                                查看详情
                            </el-button>
                            <el-button type="danger" link size="small" @click="deleteItem(row.id)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="flex justify-end mt-4">
                <el-pagination
                    v-model:current-page="expressLogTable.page"
                    v-model:page-size="expressLogTable.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="expressLogTable.total"
                    @size-change="loadExpressLogList"
                    @current-change="loadExpressLogList"
                />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { 
    Refresh, 
    Download, 
    Delete, 
    CopyDocument, 
    Van, 
    Clock, 
    CircleCheck, 
    CloseBold, 
    Remove 
} from '@element-plus/icons-vue'
import { 
    getExpressLogList, 
    deleteExpressLog, 
    batchDeleteExpressLog, 
    exportExpressLog,
    getTypeCodeOptions,
    getFeeOverOptions,
    getExpressLogStatistics
} from '@/addon/yz_she/api/express-log'

const router = useRouter()

// 表格数据
const expressLogTable = reactive({
    page: 1,
    limit: 20,
    total: 0,
    loading: false,
    data: [],
    searchParam: {
        waybill: '',
        shopbill: '',
        type_code: '',
        fee_over: '',
        courier_name: '',
        courier_phone: '',
        create_time: []
    },
    selectIds: []
})

// 统计数据
const statistics = ref({
    total: 0,
    pending: 0,
    shipping: 0,
    delivered: 0,
    rejected: 0,
    cancelled: 0
})

// 选项数据
const typeCodeOptions = ref([])
const feeOverOptions = ref([])
const exportLoading = ref(false)

/**
 * 获取物流日志列表
 */
const loadExpressLogList = () => {
    expressLogTable.loading = true
    const searchData = {
        page: expressLogTable.page,
        limit: expressLogTable.limit,
        ...expressLogTable.searchParam
    }

    getExpressLogList(searchData).then(res => {
        expressLogTable.loading = false
        expressLogTable.data = res.data.data
        expressLogTable.total = res.data.total
    }).catch(() => {
        expressLogTable.loading = false
    })
}

/**
 * 获取统计数据
 */
const loadStatistics = () => {
    const searchData = {
        create_time: expressLogTable.searchParam.create_time
    }
    
    getExpressLogStatistics(searchData).then(res => {
        statistics.value = res.data
    })
}

/**
 * 获取选项数据
 */
const loadOptions = () => {
    getTypeCodeOptions().then(res => {
        typeCodeOptions.value = res.data
    })
    
    getFeeOverOptions().then(res => {
        feeOverOptions.value = res.data
    })
}

/**
 * 重置搜索表单
 */
const resetForm = () => {
    expressLogTable.searchParam = {
        waybill: '',
        shopbill: '',
        type_code: '',
        fee_over: '',
        courier_name: '',
        courier_phone: '',
        create_time: []
    }
    expressLogTable.page = 1
    loadExpressLogList()
    loadStatistics()
}

/**
 * 刷新数据
 */
const refreshData = () => {
    loadExpressLogList()
    loadStatistics()
}

/**
 * 查看详情
 */
const viewDetail = (id: number) => {
    router.push(`/yz_she/express-log/detail/${id}`)
}

/**
 * 删除单个项目
 */
const deleteItem = (id: number) => {
    ElMessageBox.confirm('确定要删除这条物流日志吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteExpressLog(id).then(() => {
            ElMessage.success('删除成功')
            loadExpressLogList()
            loadStatistics()
        })
    })
}

/**
 * 批量删除
 */
const batchDelete = () => {
    if (expressLogTable.selectIds.length === 0) {
        ElMessage.warning('请选择要删除的数据')
        return
    }

    ElMessageBox.confirm(`确定要删除选中的 ${expressLogTable.selectIds.length} 条物流日志吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        batchDeleteExpressLog(expressLogTable.selectIds).then(() => {
            ElMessage.success('批量删除成功')
            expressLogTable.selectIds = []
            loadExpressLogList()
            loadStatistics()
        })
    })
}

/**
 * 导出数据
 */
const exportData = () => {
    exportLoading.value = true
    exportExpressLog(expressLogTable.searchParam).then(res => {
        exportLoading.value = false
        // 这里可以处理导出逻辑
        ElMessage.success('导出成功')
    }).catch(() => {
        exportLoading.value = false
    })
}

/**
 * 复制文本
 */
const copyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

/**
 * 处理表格选择变化
 */
const handleSelectionChange = (selection: any[]) => {
    expressLogTable.selectIds = selection.map(item => item.id)
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (typeCode: number) => {
    const tagTypes: Record<number, string> = {
        1: 'warning',   // 待揽收
        2: 'primary',   // 运输中
        3: 'success',   // 已签收
        4: 'danger',    // 拒收退回
        99: 'info'      // 已取消
    }
    return tagTypes[typeCode] || 'info'
}

onMounted(() => {
    loadOptions()
    loadExpressLogList()
    loadStatistics()
})
</script>

<style lang="scss" scoped>
.stat-card {
    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
}
</style>
