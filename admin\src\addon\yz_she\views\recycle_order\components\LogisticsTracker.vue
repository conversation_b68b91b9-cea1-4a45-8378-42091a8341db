<template>
    <div class="logistics-tracker">
        <div class="logistics-header mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <el-icon class="text-blue-500"><Van /></el-icon>
                    <span class="font-medium">物流跟踪</span>
                </div>
                <el-button type="primary" link size="small" @click="refreshLogistics" :loading="loading">
                    <el-icon><Refresh /></el-icon>
                    刷新
                </el-button>
            </div>
        </div>

        <!-- 物流基本信息 -->
        <div class="logistics-info mb-4" v-if="logisticsInfo.express_number">
            <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="快递公司">
                    <div class="flex items-center space-x-2">
                        <span>{{ logisticsInfo.express_company_name }}</span>
                        <el-tag size="small" type="primary">{{ logisticsInfo.express_company }}</el-tag>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="快递单号">
                    <div class="flex items-center space-x-2">
                        <span class="font-mono">{{ logisticsInfo.express_number }}</span>
                        <el-button type="primary" link size="small" @click="copyText(logisticsInfo.express_number)">
                            <el-icon><CopyDocument /></el-icon>
                        </el-button>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="物流状态">
                    <el-tag :type="getLogisticsStatusType(logisticsInfo.status)">
                        {{ logisticsInfo.status_text }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="最后更新">
                    {{ logisticsInfo.last_update_time }}
                </el-descriptions-item>
            </el-descriptions>
        </div>

        <!-- 物流轨迹 -->
        <div class="logistics-timeline" v-if="logisticsTraces.length > 0">
            <h4 class="text-sm font-medium mb-3">物流轨迹</h4>
            <el-timeline>
                <el-timeline-item
                    v-for="(trace, index) in logisticsTraces"
                    :key="index"
                    :timestamp="trace.time"
                    :type="getTraceType(trace, index)"
                    :icon="getTraceIcon(trace, index)"
                    size="large"
                >
                    <div class="trace-content">
                        <h5 class="font-medium">{{ trace.status }}</h5>
                        <p class="text-gray-600 text-sm mt-1">{{ trace.context }}</p>
                        <p class="text-gray-500 text-xs mt-1" v-if="trace.location">
                            <el-icon><Location /></el-icon>
                            {{ trace.location }}
                        </p>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </div>

        <!-- 静态演示数据 -->
        <div class="logistics-demo" v-else-if="showDemo">
            <el-alert
                title="物流跟踪演示"
                type="info"
                description="以下为物流跟踪演示数据，实际使用时将对接真实物流接口"
                show-icon
                :closable="false"
                class="mb-4"
            />
            
            <el-descriptions :column="2" border size="small" class="mb-4">
                <el-descriptions-item label="快递公司">
                    <div class="flex items-center space-x-2">
                        <span>顺丰速运</span>
                        <el-tag size="small" type="primary">SF</el-tag>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="快递单号">
                    <div class="flex items-center space-x-2">
                        <span class="font-mono">SF1234567890123</span>
                        <el-button type="primary" link size="small" @click="copyText('SF1234567890123')">
                            <el-icon><CopyDocument /></el-icon>
                        </el-button>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="物流状态">
                    <el-tag type="success">已签收</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="最后更新">
                    {{ new Date().toLocaleString() }}
                </el-descriptions-item>
            </el-descriptions>

            <h4 class="text-sm font-medium mb-3">物流轨迹</h4>
            <el-timeline>
                <el-timeline-item
                    v-for="(trace, index) in demoTraces"
                    :key="index"
                    :timestamp="trace.time"
                    :type="trace.type"
                    :icon="trace.icon"
                    size="large"
                >
                    <div class="trace-content">
                        <h5 class="font-medium">{{ trace.status }}</h5>
                        <p class="text-gray-600 text-sm mt-1">{{ trace.context }}</p>
                        <p class="text-gray-500 text-xs mt-1" v-if="trace.location">
                            <el-icon><Location /></el-icon>
                            {{ trace.location }}
                        </p>
                    </div>
                </el-timeline-item>
            </el-timeline>
        </div>

        <!-- 无物流信息 -->
        <div class="no-logistics text-center py-8" v-else>
            <el-icon class="text-gray-400 text-4xl mb-2"><Box /></el-icon>
            <p class="text-gray-500">暂无物流信息</p>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Van, Refresh, CopyDocument, Location, Box } from '@element-plus/icons-vue'

interface LogisticsInfo {
    express_company: string
    express_company_name: string
    express_number: string
    status: number
    status_text: string
    last_update_time: string
}

interface LogisticsTrace {
    time: string
    status: string
    context: string
    location?: string
}

const props = defineProps<{
    orderInfo: any
    showDemo?: boolean
}>()

const loading = ref(false)
const logisticsInfo = ref<LogisticsInfo>({} as LogisticsInfo)
const logisticsTraces = ref<LogisticsTrace[]>([])

// 演示数据
const demoTraces = ref([
    {
        time: '2024-01-15 14:30:00',
        status: '已签收',
        context: '您的快件已签收，感谢使用顺丰速运',
        location: '北京市朝阳区',
        type: 'success',
        icon: 'CircleCheck'
    },
    {
        time: '2024-01-15 09:15:00',
        status: '派件中',
        context: '快件正在派送中，派送员：张师傅，联系电话：138****1234',
        location: '北京市朝阳区营业点',
        type: 'primary',
        icon: 'Van'
    },
    {
        time: '2024-01-15 06:20:00',
        status: '到达目的地',
        context: '快件已到达北京市朝阳区营业点',
        location: '北京市朝阳区营业点',
        type: 'primary',
        icon: 'Location'
    },
    {
        time: '2024-01-14 22:45:00',
        status: '运输中',
        context: '快件正在运输途中',
        location: '北京转运中心',
        type: 'info',
        icon: 'Van'
    },
    {
        time: '2024-01-14 18:30:00',
        status: '已揽收',
        context: '快件已被顺丰速运揽收',
        location: '上海市浦东新区营业点',
        type: 'warning',
        icon: 'Box'
    }
])

/**
 * 获取物流状态类型
 */
const getLogisticsStatusType = (status: number) => {
    const types: Record<number, string> = {
        0: 'info',      // 暂无轨迹
        1: 'warning',   // 已揽收
        2: 'primary',   // 运输中
        3: 'success',   // 已签收
        4: 'danger'     // 异常
    }
    return types[status] || 'info'
}

/**
 * 获取轨迹类型
 */
const getTraceType = (trace: LogisticsTrace, index: number) => {
    if (index === 0) return 'success'
    if (trace.status.includes('异常') || trace.status.includes('失败')) return 'danger'
    if (trace.status.includes('派送') || trace.status.includes('运输')) return 'primary'
    return 'info'
}

/**
 * 获取轨迹图标
 */
const getTraceIcon = (trace: LogisticsTrace, index: number) => {
    if (index === 0 && trace.status.includes('签收')) return 'CircleCheck'
    if (trace.status.includes('派送')) return 'Van'
    if (trace.status.includes('到达')) return 'Location'
    if (trace.status.includes('揽收')) return 'Box'
    return 'InfoFilled'
}

/**
 * 复制文本
 */
const copyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

/**
 * 刷新物流信息
 */
const refreshLogistics = () => {
    if (!props.orderInfo.express_number) {
        ElMessage.warning('暂无快递单号')
        return
    }
    
    loading.value = true
    
    // 这里应该调用真实的物流查询API
    setTimeout(() => {
        loading.value = false
        ElMessage.success('物流信息已更新')
    }, 1000)
}

onMounted(() => {
    if (props.orderInfo.express_number && !props.showDemo) {
        refreshLogistics()
    }
})
</script>

<style lang="scss" scoped>
.logistics-tracker {
    .trace-content {
        h5 {
            margin: 0;
        }
        p {
            margin: 0;
        }
    }
}
</style>
