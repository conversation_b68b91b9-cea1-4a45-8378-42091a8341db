<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">回收订单管理</span>
                <div class="flex items-center space-x-2">
                    <el-button type="primary" @click="refreshData()">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button type="success" @click="exportData()">
                        <el-icon><Download /></el-icon>
                        导出
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 搜索表单 -->
        <el-card class="box-card !border-none mt-[10px] table-search-wrap" shadow="never">
            <el-form :inline="true" :model="recycleOrderTable.searchParam" ref="searchFormRef">
                <el-form-item label="订单信息" prop="search_name">
                    <el-select v-model="recycleOrderTable.searchParam.search_type" clearable class="input-item">
                        <el-option label="订单编号" value="order_no"></el-option>
                        <el-option label="商品名称" value="product_name"></el-option>
                        <el-option label="商品编码" value="product_code"></el-option>
                        <el-option label="发件人手机号" value="pickup_contact_phone"></el-option>
                    </el-select>
                    <el-input class="input-item ml-3" v-model.trim="recycleOrderTable.searchParam.search_name" placeholder="请输入搜索内容" />
                </el-form-item>
                <el-form-item label="用户信息" prop="keyword">
                    <el-input class="w-[200px]" v-model.trim="recycleOrderTable.searchParam.keyword" placeholder="用户昵称/手机号" />
                </el-form-item>
                <el-form-item label="订单来源" prop="source_type">
                    <el-select v-model="recycleOrderTable.searchParam.source_type" clearable class="input-item">
                        <el-option label="全部" value="" />
                        <el-option
                            v-for="(item, index) in sourceTypeOptions"
                            :key="index"
                            :label="item"
                            :value="index"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="配送方式" prop="delivery_type">
                    <el-select v-model="recycleOrderTable.searchParam.delivery_type" clearable class="input-item">
                        <el-option label="全部" value="" />
                        <el-option
                            v-for="(item, index) in deliveryTypeOptions"
                            :key="index"
                            :label="item"
                            :value="index"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间" prop="create_time">
                    <el-date-picker
                        v-model="recycleOrderTable.searchParam.create_time"
                        type="datetimerange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    />
                </el-form-item>
                <el-form-item label="结算时间" prop="settlement_time">
                    <el-date-picker
                        v-model="recycleOrderTable.searchParam.settlement_time"
                        type="datetimerange"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="loadRecycleOrderList()">搜索</el-button>
                    <el-button @click="resetForm(searchFormRef)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 状态标签页 -->
        <el-card class="box-card !border-none mt-[10px]" shadow="never">
            <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleTabClick">
                <el-tab-pane label="全部" name=""></el-tab-pane>
                <el-tab-pane label="待取件" name="1">
                    <template #label>
                        <el-badge :value="statusCounts.pickup_pending || 0" :hidden="!statusCounts.pickup_pending">
                            待取件
                        </el-badge>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="待收货" name="2">
                    <template #label>
                        <el-badge :value="statusCounts.receive_pending || 0" :hidden="!statusCounts.receive_pending">
                            待收货
                        </el-badge>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="待质检" name="3">
                    <template #label>
                        <el-badge :value="statusCounts.quality_pending || 0" :hidden="!statusCounts.quality_pending">
                            待质检
                        </el-badge>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="待确认" name="4">
                    <template #label>
                        <el-badge :value="statusCounts.confirm_pending || 0" :hidden="!statusCounts.confirm_pending">
                            待确认
                        </el-badge>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="已完成" name="7"></el-tab-pane>
                <el-tab-pane label="已退回" name="6"></el-tab-pane>
            </el-tabs>

            <!-- 批量操作工具栏 -->
            <div class="mb-[10px] flex items-center justify-between" v-if="activeName == '3' || activeName == '4'">
                <div class="flex items-center space-x-2">
                    <el-button @click="batchQualityCheck" size="small" v-if="activeName == '3'" :disabled="!recycleOrderTable.selectData.length">
                        <el-icon><Check /></el-icon>
                        批量质检
                    </el-button>
                    <el-button @click="batchSettlement" size="small" v-if="activeName == '7'" :disabled="!recycleOrderTable.selectData.length">
                        <el-icon><Money /></el-icon>
                        批量结算
                    </el-button>
                </div>
                <div class="text-sm text-gray-500">
                    已选择 {{ recycleOrderTable.selectData.length }} 个订单
                </div>
            </div>



            <!-- 表格内容 -->
            <div class="table-body min-h-[150px]" v-loading="recycleOrderTable.loading">
                <div v-if="!recycleOrderTable.loading">
                    <template v-if="recycleOrderTable.data.length">
                        <div v-for="(item, index) in recycleOrderTable.data" :key="index">
                            <!-- 订单头部信息 -->
                            <div class="flex items-center justify-between bg-[#f7f8fa] mt-[10px] border-[#e4e7ed] border-solid border-b-[1px] px-3 h-[35px] text-[12px] text-[666]">
                                <div class="ml-2">
                                    <span>订单编号：{{ item.order_no }}</span>
                                    <span class="ml-6">创建时间：{{ item.create_time_text }}</span>
                                    <span class="ml-5">订单来源：{{ item.source_type_text }}</span>
                                    <span class="ml-5" v-if="item.pickup_time">期望取件时间：{{ item.pickup_time }}</span>
                                </div>
                                <div>
                                    <el-button type="primary" link @click="toDetail(item)">详情</el-button>
                                    <el-button type="primary" link @click="setNotes(item)">备注</el-button>
                                    <el-button type="primary" link @click="viewLogs(item)">日志</el-button>
                                </div>
                            </div>

                            <!-- 订单详细信息表格 -->
                            <el-table :data="[item]" size="large" :show-header="false" ref="multipleTable" @select="handleSelectChange">
                                <el-table-column type="selection" width="40" />

                                <!-- 商品信息 -->
                                <el-table-column align="left" min-width="220">
                                    <template #default="{ row }">
                                        <div class="flex cursor-pointer">
                                            <!-- 商品图片区域 -->
                                            <div class="flex items-center min-w-[50px] mr-[12px] relative">
                                                <!-- 估价回收：显示商品图片 -->
                                                <template v-if="row.source_type == 1">
                                                    <img class="w-[50px] h-[50px] rounded object-cover" v-if="row.product_image" :src="img(row.product_image)" alt="商品图片" />
                                                    <div class="w-[50px] h-[50px] bg-blue-100 rounded flex items-center justify-center text-blue-600" v-else>
                                                        <el-icon size="20"><Picture /></el-icon>
                                                    </div>
                                                    <el-tag size="small" type="primary" class="absolute -top-1 -right-1 scale-75">估价</el-tag>
                                                </template>
                                                <!-- 直收订单：显示商品图片 -->
                                                <template v-else-if="row.source_type == 2">
                                                    <img class="w-[50px] h-[50px] rounded object-cover" v-if="row.product_image" :src="img(row.product_image)" alt="商品图片" />
                                                    <div class="w-[50px] h-[50px] bg-green-100 rounded flex items-center justify-center text-green-600" v-else>
                                                        <el-icon size="20"><Picture /></el-icon>
                                                    </div>
                                                    <el-tag size="small" type="success" class="absolute -top-1 -right-1 scale-75">直收</el-tag>
                                                </template>
                                                <!-- 批量回收：使用图标代替商品图片 -->
                                                <template v-else-if="row.source_type == 3">
                                                    <div class="w-[50px] h-[50px] bg-orange-100 rounded flex items-center justify-center text-orange-600">
                                                        <el-icon size="24"><Box /></el-icon>
                                                    </div>
                                                    <el-tag size="small" type="warning" class="absolute -top-1 -right-1 scale-75">批量</el-tag>
                                                </template>
                                            </div>

                                            <!-- 商品信息区域 -->
                                            <div class="flex flex-col items-start flex-1 min-w-0">
                                                <!-- 商品名称 -->
                                                <el-tooltip class="box-item" effect="light" placement="top">
                                                    <template #content>
                                                        <div class="max-w-[300px]">{{ getProductDisplayName(row) }}</div>
                                                    </template>
                                                    <p class="multi-hidden text-[14px] font-medium text-gray-800 leading-5">
                                                        {{ getProductDisplayName(row) }}
                                                    </p>
                                                </el-tooltip>

                                                <!-- 根据订单来源显示不同信息 -->
                                                <template v-if="row.source_type == 1">
                                                    <!-- 估价回收信息 -->
                                                    <div class="flex flex-col gap-1 mt-1">
                                                        <span class="text-[12px] text-gray-500 truncate">品牌：{{ row.brand?.name || '-' }}</span>
                                                        <span class="text-[12px] text-blue-600 truncate cursor-pointer hover:text-blue-800"
                                                              @click="viewQuoteOrder(row.quote_order)"
                                                              v-if="row.quote_order?.order_no">
                                                            估价单号：{{ row.quote_order.order_no }}
                                                        </span>
                                                        <span class="text-[12px] text-gray-500 truncate" v-else>
                                                            估价单号：-
                                                        </span>
                                                    </div>
                                                </template>
                                                <template v-else-if="row.source_type == 2">
                                                    <!-- 直收订单信息 -->
                                                    <div class="flex flex-col gap-1 mt-1">
                                                        <span class="text-[12px] text-gray-500 truncate">货号：{{ row.product_code || '-' }}</span>
                                                        <span class="text-[12px] text-gray-500 truncate">品牌：{{ row.brand?.name || '-' }}</span>
                                                    </div>
                                                </template>
                                                <template v-else-if="row.source_type == 3">
                                                    <!-- 批量回收信息 -->
                                                    <div class="flex flex-col gap-1 mt-1">
                                                        <span class="text-[12px] text-gray-500 truncate">数量：{{ row.quantity }}件</span>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 价格信息 -->
                                <el-table-column min-width="120">
                                    <template #default="{ row }">
                                        <div class="flex flex-col">
                                            <span class="text-[13px] text-primary">预期：¥{{ row.expected_price }}</span>
                                            <span class="text-[13px] text-warning" v-if="row.voucher_amount > 0">加价券：+¥{{ row.voucher_amount }}</span>
                                            <span class="text-[13px] text-success" v-if="row.final_price">最终：¥{{ row.final_price }}</span>
                                            <span class="text-[13px] text-danger font-medium" v-if="row.total_amount">结算：¥{{ row.total_amount }}</span>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 用户信息 -->
                                <el-table-column min-width="140">
                                    <template #default="{ row }">
                                        <div class="flex flex-col">
                                            <!-- 会员信息 -->
                                            <span class="text-[12px] text-primary cursor-pointer hover:text-blue-700"
                                                  @click="memberEvent(row.member.member_id)">
                                                {{ row.member?.nickname || '-' }}
                                            </span>
                                            <span class="text-[12px] mt-[2px] text-gray-600">{{ row.member?.mobile || '-' }}</span>

                                            <!-- 快递上门显示地址信息 -->
                                            <template v-if="row.delivery_type == 1">
                                                <div class="mt-2 p-2 bg-blue-50 rounded text-[11px] text-gray-700">
                                                    <div class="font-medium text-blue-700 mb-1">
                                                        <el-icon class="mr-1"><LocationFilled /></el-icon>上门地址
                                                    </div>
                                                    <div class="space-y-1">
                                                        <div>{{ row.pickup_address?.contact_name || '-' }} {{ row.pickup_address?.mobile || '-' }}</div>
                                                        <div class="line-clamp-2">{{ row.pickup_address?.full_address || '-' }}</div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 配送信息 -->
                                <el-table-column min-width="160">
                                    <template #default="{ row }">
                                        <div class="flex flex-col">
                                            <!-- 配送方式 -->
                                            <div class="flex items-center space-x-1 mb-2">
                                                <el-icon v-if="row.delivery_type == 1" class="text-blue-500"><Van /></el-icon>
                                                <el-icon v-else class="text-green-500"><Box /></el-icon>
                                                <span class="text-[14px] font-medium">{{ row.delivery_type_text }}</span>
                                            </div>

                                            <!-- 物流信息 -->
                                            <div v-if="row.express_number" class="mb-2">
                                                <div class="flex items-center space-x-1">
                                                    <span class="text-[12px] text-[#666]">{{ row.express_company || 'SF' }}</span>
                                                    <el-tag size="small" :type="getExpressStatusType(row.express_status)">
                                                        {{ getExpressStatusText(row.express_status) }}
                                                    </el-tag>
                                                </div>
                                                <div class="text-[11px] text-[#999] font-mono mt-1">{{ row.express_number }}</div>
                                            </div>

                                            <!-- 地址信息 -->
                                            <div class="border-t pt-2">
                                                <div class="text-[12px] text-[#333] font-medium">{{ row.pickup_contact_name }}</div>
                                                <div class="text-[11px] text-[#666]">{{ row.pickup_contact_phone }}</div>
                                                <div class="text-[11px] text-[#999] mt-1 line-clamp-2" :title="row.pickup_address_detail">
                                                    {{ row.pickup_address_detail }}
                                                </div>
                                            </div>

                                            <!-- 用户ID -->
                                            <div class="text-[11px] text-[#999] mt-2">
                                                ID：{{ row.member?.member_id || '-' }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 订单状态 -->
                                <el-table-column min-width="100">
                                    <template #default="{ row }">
                                        <div class="flex flex-col items-center">
                                            <el-tag :type="getStatusTagType(row.status)" class="mb-1">{{ row.status_text }}</el-tag>
                                            <el-tag :type="row.settlement_status == 1 ? 'success' : 'info'" size="small">
                                                {{ row.settlement_status == 1 ? '已结算' : '未结算' }}
                                            </el-tag>
                                        </div>
                                    </template>
                                </el-table-column>

                                <!-- 操作按钮 -->
                                <el-table-column align="right" min-width="120">
                                    <template #default="{ row }">
                                        <div class="flex flex-col space-y-1">
                                            <!-- 根据状态显示不同操作 -->
                                            <template v-if="row.status == 1">
                                                <el-button type="primary" link size="small" @click="updateExpressInfo(row)">更新快递</el-button>
                                            </template>
                                            <template v-if="row.status == 2">
                                                <el-button type="primary" link size="small" @click="receiveOrder(row)">收货确认</el-button>
                                            </template>
                                            <template v-if="row.status == 3">
                                                <el-button type="primary" link size="small" @click="startQuality(row)">开始质检</el-button>
                                            </template>
                                            <template v-if="row.status == 4">
                                                <el-button type="primary" link size="small" @click="completeQuality(row)">完成质检</el-button>
                                            </template>
                                            <template v-if="row.status == 5">
                                                <el-button type="primary" link size="small" @click="returnOrder(row)">确认退回</el-button>
                                            </template>
                                            <template v-if="row.status == 7 && row.settlement_status == 0">
                                                <el-button type="success" link size="small" @click="settlement(row)">结算订单</el-button>
                                            </template>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 订单备注 -->
                            <div v-if="item.admin_note" class="text-[14px] min-h-[30px] leading-[30px] px-3 bg-[#fff0e5] text-[#ff7f5b]">
                                <span class="mr-[5px]">备注：</span>
                                <span>{{ item.admin_note }}</span>
                            </div>
                        </div>
                    </template>
                    <el-empty v-else :image-size="100" description="暂无数据" />
                </div>
            </div>

            <div class="mt-[16px] flex justify-end">
                <el-pagination
                    v-model:current-page="recycleOrderTable.page"
                    v-model:page-size="recycleOrderTable.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="recycleOrderTable.total"
                    @size-change="loadRecycleOrderList()"
                    @current-change="loadRecycleOrderList()"
                />
            </div>
        </el-card>

        <!-- 收货确认对话框 -->
        <el-dialog v-model="receiveDialog.showDialog" title="收货确认" width="500px">
            <el-form :model="receiveDialog.formData" label-width="100px" ref="receiveFormRef">
                <el-form-item label="订单信息">
                    <div class="text-sm text-gray-600">
                        <p>订单编号：{{ receiveDialog.orderInfo.order_no }}</p>
                        <p>商品名称：{{ receiveDialog.orderInfo.product_name }}</p>
                        <p>预期价格：¥{{ receiveDialog.orderInfo.expected_price }}</p>
                    </div>
                </el-form-item>
                <el-form-item label="收货状态" required>
                    <el-radio-group v-model="receiveDialog.formData.receive_status">
                        <el-radio :label="1">正常收货</el-radio>
                        <el-radio :label="2">商品有问题</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="收货备注">
                    <el-input
                        v-model="receiveDialog.formData.receive_note"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入收货备注，如有问题请详细说明"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="receiveDialog.showDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmReceive()" :loading="receiveDialog.loading">确认收货</el-button>
            </template>
        </el-dialog>

        <!-- 质检对话框 -->
        <el-dialog v-model="qualityDialog.showDialog" title="质检操作" width="700px">
            <el-form :model="qualityDialog.formData" label-width="100px" ref="qualityFormRef" :rules="qualityRules">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="订单信息">
                            <div class="text-sm text-gray-600">
                                <p>订单编号：{{ qualityDialog.orderInfo.order_no }}</p>
                                <p>商品名称：{{ qualityDialog.orderInfo.product_name }}</p>
                                <p>预期价格：¥{{ qualityDialog.orderInfo.expected_price }}</p>
                                <p>加价券：¥{{ qualityDialog.orderInfo.voucher_amount || 0 }}</p>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="质检评分" prop="quality_score" required>
                            <el-input-number
                                v-model="qualityDialog.formData.quality_score"
                                :min="0"
                                :max="100"
                                placeholder="请输入质检评分(0-100)"
                                class="w-full"
                            />
                            <div class="text-xs text-gray-500 mt-1">
                                评分标准：90-100优秀，80-89良好，70-79一般，60-69较差，60以下很差
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="最终价格" prop="final_price" required>
                    <el-input-number
                        v-model="qualityDialog.formData.final_price"
                        :min="0"
                        :precision="2"
                        placeholder="请输入最终回收价格"
                        class="w-[200px]"
                    />
                    <span class="ml-2 text-sm text-gray-500">
                        建议价格：¥{{ getSuggestedPrice() }}
                    </span>
                </el-form-item>

                <el-form-item label="质检说明" prop="quality_note">
                    <el-input
                        v-model="qualityDialog.formData.quality_note"
                        type="textarea"
                        :rows="4"
                        placeholder="请详细说明质检情况，包括商品外观、功能、配件等"
                    />
                </el-form-item>

                <el-form-item label="质检图片">
                    <upload-image
                        v-model="qualityDialog.formData.quality_images"
                        :limit="9"
                    />
                    <div class="text-xs text-gray-500 mt-1">
                        建议上传商品的正面、背面、细节等照片，最多9张
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="qualityDialog.showDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmQuality()" :loading="qualityDialog.loading">完成质检</el-button>
            </template>
        </el-dialog>

        <!-- 快递信息更新对话框 -->
        <el-dialog v-model="expressDialog.showDialog" title="更新快递信息" width="500px">
            <el-form :model="expressDialog.formData" label-width="100px" ref="expressFormRef">
                <el-form-item label="快递公司" required>
                    <el-select v-model="expressDialog.formData.express_company" placeholder="请选择快递公司" class="w-full">
                        <el-option label="顺丰速运" value="SF" />
                        <el-option label="圆通速递" value="YTO" />
                        <el-option label="中通快递" value="ZTO" />
                        <el-option label="申通快递" value="STO" />
                        <el-option label="韵达速递" value="YD" />
                        <el-option label="百世快递" value="HTKY" />
                        <el-option label="德邦快递" value="DBL" />
                        <el-option label="京东快递" value="JD" />
                    </el-select>
                </el-form-item>
                <el-form-item label="快递单号" required>
                    <el-input
                        v-model="expressDialog.formData.express_number"
                        placeholder="请输入快递单号"
                    />
                </el-form-item>
                <el-form-item label="快递费用">
                    <el-input-number
                        v-model="expressDialog.formData.express_fee"
                        :min="0"
                        :precision="2"
                        placeholder="快递费用"
                        class="w-full"
                    />
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="expressDialog.formData.express_note"
                        type="textarea"
                        :rows="2"
                        placeholder="快递相关备注"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="expressDialog.showDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmExpress()" :loading="expressDialog.loading">确认更新</el-button>
            </template>
        </el-dialog>

        <!-- 订单备注对话框 -->
        <el-dialog v-model="notesDialog.showDialog" title="订单备注" width="500px">
            <el-form :model="notesDialog.formData" label-width="100px">
                <el-form-item label="管理员备注">
                    <el-input
                        v-model="notesDialog.formData.admin_note"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入管理员备注"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="notesDialog.showDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmNotes()" :loading="notesDialog.loading">保存备注</el-button>
            </template>
        </el-dialog>

        <!-- 订单日志对话框 -->
        <el-dialog v-model="logsDialog.showDialog" title="订单操作日志" width="800px">
            <el-table :data="logsDialog.data" v-loading="logsDialog.loading">
                <el-table-column prop="create_time_text" label="操作时间" width="160" />
                <el-table-column prop="operator_name" label="操作人" width="120" />
                <el-table-column prop="from_status_text" label="原状态" width="100" />
                <el-table-column prop="to_status_text" label="新状态" width="100" />
                <el-table-column prop="change_reason" label="变更原因" min-width="150" />
                <el-table-column prop="remark" label="备注" min-width="150" />
            </el-table>
            <template #footer>
                <el-button @click="logsDialog.showDialog = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { ArrowDown, Refresh, Download, Check, Money, Picture, Star, Camera, Box, Van, LocationFilled } from '@element-plus/icons-vue'
import {
    getRecycleOrderList,
    getRecycleOrderStatusOptions,
    getRecycleOrderSourceTypeOptions,
    getRecycleOrderDeliveryTypeOptions,
    getRecycleOrderStatusCounts,
    receiveRecycleOrder,
    startQualityCheck,
    completeQualityCheck,
    settlementRecycleOrder,
    returnRecycleOrder,
    updateExpressInfo,
    updateOrderNotes,
    getOrderLogs,
    batchRecycleOrder,
    exportRecycleOrder
} from '@/addon/yz_she/api/recycle_order'
import { useRoute, useRouter } from 'vue-router'
import { img } from '@/utils/common'
import UploadImage from '@/components/upload-image/index.vue'

const route = useRoute()
const router = useRouter()
const searchFormRef = ref<FormInstance>()
const receiveFormRef = ref<FormInstance>()
const qualityFormRef = ref<FormInstance>()
const expressFormRef = ref<FormInstance>()
const recycleOrderTableRef = ref()

// 当前激活的标签页
const activeName = ref('')

// 状态统计数据
const statusCounts = ref({})

// 选项数据
const statusOptions = ref({})
const sourceTypeOptions = ref({})
const deliveryTypeOptions = ref({})

// 回收订单列表
const recycleOrderTable = reactive({
    page: 1,
    limit: 10,
    total: 0,
    loading: true,
    data: [],
    searchParam: {
        search_type: 'order_no',
        search_name: '',
        keyword: '',
        status: '',
        source_type: '',
        delivery_type: '',
        create_time: [],
        settlement_time: []
    },
    selectData: []
})

// 收货对话框
const receiveDialog = reactive({
    showDialog: false,
    loading: false,
    orderId: 0,
    orderInfo: {},
    formData: {
        receive_status: 1,
        receive_note: ''
    }
})

// 质检对话框
const qualityDialog = reactive({
    showDialog: false,
    loading: false,
    orderId: 0,
    orderInfo: {},
    formData: {
        quality_score: 85,
        final_price: 0,
        quality_note: '',
        quality_images: []
    }
})

// 快递信息对话框
const expressDialog = reactive({
    showDialog: false,
    loading: false,
    orderId: 0,
    formData: {
        express_company: '',
        express_number: '',
        express_fee: 0,
        express_note: ''
    }
})

// 备注对话框
const notesDialog = reactive({
    showDialog: false,
    loading: false,
    orderId: 0,
    formData: {
        admin_note: ''
    }
})

// 日志对话框
const logsDialog = reactive({
    showDialog: false,
    loading: false,
    orderId: 0,
    data: []
})

// 质检表单验证规则
const qualityRules = {
    quality_score: [
        { required: true, message: '请输入质检评分', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '评分范围为0-100', trigger: 'blur' }
    ],
    final_price: [
        { required: true, message: '请输入最终价格', trigger: 'blur' },
        { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
    ],
    quality_note: [
        { required: true, message: '请输入质检说明', trigger: 'blur' },
        { min: 10, message: '质检说明至少10个字符', trigger: 'blur' }
    ]
}

/**
 * 获取回收订单列表
 */
const loadRecycleOrderList = (page: number = 1) => {
    recycleOrderTable.loading = true
    recycleOrderTable.page = page

    const searchParam = {
        page: recycleOrderTable.page,
        limit: recycleOrderTable.limit,
        status: activeName.value, // 使用当前激活的标签页状态
        ...recycleOrderTable.searchParam
    }

    getRecycleOrderList(searchParam).then(res => {
        recycleOrderTable.loading = false
        recycleOrderTable.data = res.data.data
        recycleOrderTable.total = res.data.total
    }).catch(() => {
        recycleOrderTable.loading = false
    })
}

/**
 * 获取状态统计数据
 */
const loadStatusCounts = () => {
    getRecycleOrderStatusCounts().then(res => {
        statusCounts.value = res.data
    })
}

/**
 * 获取选项数据
 */
const loadOptions = () => {
    // 获取状态选项
    getRecycleOrderStatusOptions().then(res => {
        statusOptions.value = res.data
    })

    // 获取订单来源选项
    getRecycleOrderSourceTypeOptions().then(res => {
        sourceTypeOptions.value = res.data
    })

    // 获取配送方式选项
    getRecycleOrderDeliveryTypeOptions().then(res => {
        deliveryTypeOptions.value = res.data
    })
}

/**
 * 标签页切换
 */
const handleTabClick = (tabName: string) => {
    activeName.value = tabName
    recycleOrderTable.searchParam.status = tabName
    loadRecycleOrderList()
}

/**
 * 刷新数据
 */
const refreshData = () => {
    loadRecycleOrderList()
    loadStatusCounts()
}

/**
 * 重置搜索表单
 */
const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    loadRecycleOrderList()
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: number) => {
    const tagTypes: Record<number, string> = {
        1: 'warning',  // 待取件
        2: 'primary',  // 待收货
        3: 'info',     // 待质检
        4: 'warning',  // 待确认
        5: 'danger',   // 待退回
        6: 'info',     // 已退回
        7: 'success'   // 已完成
    }
    return tagTypes[status] || 'info'
}

/**
 * 获取快递状态类型
 */
const getExpressStatusType = (status: number) => {
    const tagTypes: Record<number, string> = {
        0: 'info',      // 暂无轨迹
        1: 'warning',   // 已揽收
        2: 'primary',   // 运输中
        3: 'success',   // 已签收
        4: 'danger'     // 异常
    }
    return tagTypes[status] || 'info'
}

/**
 * 获取快递状态文本
 */
const getExpressStatusText = (status: number) => {
    const statusTexts: Record<number, string> = {
        0: '暂无轨迹',
        1: '已揽收',
        2: '运输中',
        3: '已签收',
        4: '异常'
    }
    return statusTexts[status] || '未知'
}

/**
 * 全选操作
 */
const selectAllCheck = (selection: any[]) => {
    recycleOrderTable.selectData = selection
}

/**
 * 单选操作
 */
const handleSelectChange = (selection: any[], row: any) => {
    recycleOrderTable.selectData = selection
}

/**
 * 查看详情
 */
const toDetail = (row: any) => {
    router.push(`/yz_she/recycle_order/detail/${row.id}`)
}

/**
 * 查看用户信息
 */
const memberEvent = (memberId: number) => {
    // 跳转到用户详情页面
    router.push(`/member/detail/${memberId}`)
}

/**
 * 收货确认
 */
const receiveOrder = (row: any) => {
    receiveDialog.orderId = row.id
    receiveDialog.orderInfo = row
    receiveDialog.formData = {
        receive_status: 1,
        receive_note: ''
    }
    receiveDialog.showDialog = true
}

/**
 * 确认收货
 */
const confirmReceive = () => {
    receiveDialog.loading = true
    const params = {
        ...receiveDialog.formData,
        admin_id: 1 // 这里应该从用户信息中获取
    }

    receiveRecycleOrder(receiveDialog.orderId, params).then(() => {
        receiveDialog.loading = false
        receiveDialog.showDialog = false
        ElMessage.success('收货成功')
        loadRecycleOrderList()
    }).catch(() => {
        receiveDialog.loading = false
    })
}

/**
 * 开始质检
 */
const startQuality = (row: any) => {
    ElMessageBox.confirm('确认开始质检该订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1 // 这里应该从用户信息中获取
        }
        startQualityCheck(row.id, params).then(() => {
            ElMessage.success('开始质检成功')
            loadRecycleOrderList()
        })
    })
}

/**
 * 完成质检
 */
const completeQuality = (row: any) => {
    qualityDialog.orderId = row.id
    qualityDialog.orderInfo = row
    qualityDialog.formData = {
        quality_score: 85,
        final_price: row.expected_price,
        quality_note: '',
        quality_images: []
    }
    qualityDialog.showDialog = true
}

/**
 * 获取建议价格
 */
const getSuggestedPrice = () => {
    const score = qualityDialog.formData.quality_score
    const expectedPrice = qualityDialog.orderInfo.expected_price || 0

    if (score >= 90) return expectedPrice
    if (score >= 80) return (expectedPrice * 0.9).toFixed(2)
    if (score >= 70) return (expectedPrice * 0.8).toFixed(2)
    if (score >= 60) return (expectedPrice * 0.7).toFixed(2)
    return (expectedPrice * 0.5).toFixed(2)
}

/**
 * 确认质检
 */
const confirmQuality = () => {
    if (!qualityFormRef.value) return

    qualityFormRef.value.validate((valid) => {
        if (!valid) return

        qualityDialog.loading = true
        const params = {
            ...qualityDialog.formData,
            admin_id: 1 // 这里应该从用户信息中获取
        }

        completeQualityCheck(qualityDialog.orderId, params).then(() => {
            qualityDialog.loading = false
            qualityDialog.showDialog = false
            ElMessage.success('质检完成')
            loadRecycleOrderList()
            loadStatusCounts()
        }).catch(() => {
            qualityDialog.loading = false
        })
    })
}

/**
 * 更新快递信息
 */
const updateExpressInfo = (row: any) => {
    expressDialog.orderId = row.id
    expressDialog.formData = {
        express_company: row.express_company || '',
        express_number: row.express_number || '',
        express_fee: row.express_fee || 0,
        express_note: ''
    }
    expressDialog.showDialog = true
}

/**
 * 确认快递信息
 */
const confirmExpress = () => {
    expressDialog.loading = true
    const params = {
        ...expressDialog.formData,
        admin_id: 1
    }

    updateExpressInfo(expressDialog.orderId, params).then(() => {
        expressDialog.loading = false
        expressDialog.showDialog = false
        ElMessage.success('快递信息更新成功')
        loadRecycleOrderList()
    }).catch(() => {
        expressDialog.loading = false
    })
}

/**
 * 设置备注
 */
const setNotes = (row: any) => {
    notesDialog.orderId = row.id
    notesDialog.formData.admin_note = row.admin_note || ''
    notesDialog.showDialog = true
}

/**
 * 确认备注
 */
const confirmNotes = () => {
    notesDialog.loading = true
    const params = {
        admin_note: notesDialog.formData.admin_note,
        admin_id: 1
    }

    updateOrderNotes(notesDialog.orderId, params).then(() => {
        notesDialog.loading = false
        notesDialog.showDialog = false
        ElMessage.success('备注保存成功')
        loadRecycleOrderList()
    }).catch(() => {
        notesDialog.loading = false
    })
}

/**
 * 查看日志
 */
const viewLogs = (row: any) => {
    logsDialog.orderId = row.id
    logsDialog.loading = true
    logsDialog.showDialog = true

    getOrderLogs(row.id).then(res => {
        logsDialog.loading = false
        logsDialog.data = res.data
    }).catch(() => {
        logsDialog.loading = false
    })
}

/**
 * 结算订单
 */
const settlement = (row: any) => {
    ElMessageBox.confirm('确认结算该订单？结算后金额将直接转入用户余额。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            settlement_note: '订单结算完成'
        }
        settlementRecycleOrder(row.id, params).then(() => {
            ElMessage.success('结算成功')
            loadRecycleOrderList()
        })
    })
}

/**
 * 退回订单
 */
const returnOrder = (row: any) => {
    ElMessageBox.prompt('请输入退回原因', '确认退回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '退回原因不能为空'
    }).then(({ value }) => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            return_note: value
        }
        returnRecycleOrder(row.id, params).then(() => {
            ElMessage.success('退回成功')
            loadRecycleOrderList()
        })
    })
}

/**
 * 批量质检
 */
const batchQualityCheck = () => {
    if (!recycleOrderTable.selectData.length) {
        ElMessage.warning('请选择要质检的订单')
        return
    }

    ElMessageBox.confirm('确认批量开始质检选中的订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const ids = recycleOrderTable.selectData.map((item: any) => item.id)
        batchRecycleOrder({
            ids,
            action: 'start_quality',
            admin_id: 1
        }).then(() => {
            ElMessage.success('批量质检操作成功')
            loadRecycleOrderList()
            loadStatusCounts()
        })
    })
}

/**
 * 批量结算
 */
const batchSettlement = () => {
    if (!recycleOrderTable.selectData.length) {
        ElMessage.warning('请选择要结算的订单')
        return
    }

    const validOrders = recycleOrderTable.selectData.filter((item: any) =>
        item.status == 7 && item.settlement_status == 0
    )

    if (!validOrders.length) {
        ElMessage.warning('选中的订单中没有可结算的订单')
        return
    }

    ElMessageBox.confirm(`确认批量结算 ${validOrders.length} 个订单？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const ids = validOrders.map((item: any) => item.id)
        batchRecycleOrder({
            ids,
            action: 'settlement',
            admin_id: 1
        }).then(() => {
            ElMessage.success('批量结算成功')
            loadRecycleOrderList()
            loadStatusCounts()
        })
    })
}

/**
 * 获取商品显示名称
 */
const getProductDisplayName = (row: any) => {
    switch (row.source_type) {
        case 1: // 估价回收
            return row.product_name || '估价回收订单'
        case 2: // 直收订单
            return row.product_name || '-'
        case 3: // 批量回收
            return '批量回收订单'
        default:
            return row.product_name || '-'
    }
}

/**
 * 查看估价订单详情
 */
const viewQuoteOrder = (quoteOrder: any) => {
    if (!quoteOrder || !quoteOrder.id) {
        ElMessage.warning('估价订单信息不存在')
        return
    }

    // 打开估价订单详情弹窗或跳转页面
    // 这里可以根据实际需求实现，比如：
    // 1. 弹窗显示估价订单详情
    // 2. 跳转到估价订单详情页面

    // 示例：跳转到估价订单详情页面
    router.push({
        path: '/addon/yz_she/quote_order/detail',
        query: { id: quoteOrder.id }
    })
}

/**
 * 导出数据
 */
const exportData = () => {
    const searchParam = {
        status: activeName.value,
        ...recycleOrderTable.searchParam
    }

    ElMessageBox.confirm('确认导出当前筛选条件下的订单数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
    }).then(() => {
        exportRecycleOrder(searchParam).then(res => {
            // 处理导出逻辑，通常是下载文件
            const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = `回收订单_${new Date().getTime()}.xlsx`
            link.click()
            window.URL.revokeObjectURL(url)
            ElMessage.success('导出成功')
        })
    })
}

onMounted(() => {
    loadOptions()
    loadRecycleOrderList()
    loadStatusCounts()
})
</script>

<style lang="scss" scoped>
.text-primary {
    color: #409eff;
}

.text-success {
    color: #67c23a;
}

.text-warning {
    color: #e6a23c;
}

.text-danger {
    color: #f56c6c;
}

.table-search-wrap {
    .input-item {
        width: 150px;
    }
}

.table-top {
    border: 1px solid var(--el-table-border-color);
    border-bottom: none;
}

.table-body {
    border: 1px solid var(--el-table-border-color);
    border-top: none;
}

.multi-hidden {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

.demo-tabs {
    .el-tabs__item {
        font-size: 14px;
        font-weight: 500;
    }
}

:deep(.el-badge__content) {
    background-color: #f56c6c;
    border-color: #f56c6c;
}

/* 文本截断样式 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    max-height: 2.8em;
}
</style>
