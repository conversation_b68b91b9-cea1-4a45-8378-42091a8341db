<template>
    <div class="main-container">
        <!-- 页面头部 -->
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <span class="text-page-title">物流日志详情</span>
                    <el-tag v-if="logInfo.id" :type="getStatusTagType(logInfo.type_code)" size="large">
                        {{ logInfo.type_code_text }}
                    </el-tag>
                </div>
                <div class="flex items-center space-x-2">
                    <el-button @click="refreshDetail()">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button @click="goBack()">返回</el-button>
                </div>
            </div>
        </el-card>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mt-4" v-loading="loading">
            <!-- 左侧主要信息 -->
            <div class="lg:col-span-2 space-y-4">
                <!-- 基本信息 -->
                <el-card class="box-card !border-none" shadow="never" v-if="logInfo.id">
                    <template #header>
                        <h3 class="text-lg font-medium">基本信息</h3>
                    </template>
                    
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="运单号" :span="2">
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-lg">{{ logInfo.waybill }}</span>
                                <el-button type="primary" link size="small" @click="copyText(logInfo.waybill)">
                                    <el-icon><CopyDocument /></el-icon>
                                </el-button>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="商家单号" v-if="logInfo.shopbill">
                            <span class="font-mono">{{ logInfo.shopbill }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="订单ID">
                            <el-button type="primary" link @click="viewOrder(logInfo.order_id)">
                                {{ logInfo.order_id }}
                            </el-button>
                        </el-descriptions-item>
                        <el-descriptions-item label="状态描述" :span="2" v-if="logInfo.type">
                            {{ logInfo.type }}
                        </el-descriptions-item>
                        <el-descriptions-item label="状态码">
                            <el-tag :type="getStatusTagType(logInfo.type_code)">
                                {{ logInfo.type_code_text }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="创建时间">
                            {{ logInfo.create_time }}
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- 重量体积信息 -->
                <el-card class="box-card !border-none" shadow="never" v-if="hasWeightInfo">
                    <template #header>
                        <h3 class="text-lg font-medium">重量体积信息</h3>
                    </template>
                    
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="下单重量" v-if="logInfo.weight > 0">
                            {{ logInfo.weight }}kg
                        </el-descriptions-item>
                        <el-descriptions-item label="站点称重" v-if="logInfo.real_weight > 0">
                            {{ logInfo.real_weight }}kg
                        </el-descriptions-item>
                        <el-descriptions-item label="分拣称重" v-if="logInfo.transfer_weight > 0">
                            {{ logInfo.transfer_weight }}kg
                        </el-descriptions-item>
                        <el-descriptions-item label="计费重量" v-if="logInfo.cal_weight > 0">
                            {{ logInfo.cal_weight }}kg
                        </el-descriptions-item>
                        <el-descriptions-item label="体积" v-if="logInfo.volume > 0">
                            {{ logInfo.volume }}m³
                        </el-descriptions-item>
                        <el-descriptions-item label="体积换算重量" v-if="logInfo.parse_weight > 0">
                            {{ logInfo.parse_weight }}kg
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- 费用信息 -->
                <el-card class="box-card !border-none" shadow="never" v-if="hasFeeInfo">
                    <template #header>
                        <h3 class="text-lg font-medium">费用信息</h3>
                    </template>
                    
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="运单总扣款费用" v-if="logInfo.total_freight > 0">
                            <span class="text-lg font-bold text-red-600">¥{{ logInfo.total_freight }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="快递费" v-if="logInfo.freight > 0">
                            ¥{{ logInfo.freight }}
                        </el-descriptions-item>
                        <el-descriptions-item label="保价费" v-if="logInfo.freight_insured > 0">
                            ¥{{ logInfo.freight_insured }}
                        </el-descriptions-item>
                        <el-descriptions-item label="增值费用" v-if="logInfo.freight_haocai > 0">
                            ¥{{ logInfo.freight_haocai }}
                        </el-descriptions-item>
                        <el-descriptions-item label="换单号" v-if="logInfo.change_bill">
                            {{ logInfo.change_bill }}
                        </el-descriptions-item>
                        <el-descriptions-item label="逆向费" v-if="logInfo.change_bill_freight > 0">
                            ¥{{ logInfo.change_bill_freight }}
                        </el-descriptions-item>
                        <el-descriptions-item label="扣费状态" :span="2">
                            <el-tag :type="logInfo.fee_over == 1 ? 'success' : 'warning'">
                                {{ logInfo.fee_over_text }}
                            </el-tag>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- 快递员信息 -->
                <el-card class="box-card !border-none" shadow="never" v-if="hasCourierInfo">
                    <template #header>
                        <h3 class="text-lg font-medium">快递员信息</h3>
                    </template>
                    
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="快递员姓名">
                            {{ logInfo.courier_name }}
                        </el-descriptions-item>
                        <el-descriptions-item label="快递员电话" v-if="logInfo.courier_phone">
                            <div class="flex items-center space-x-2">
                                <span>{{ logInfo.courier_phone }}</span>
                                <el-button type="primary" link size="small" @click="callPhone(logInfo.courier_phone)">
                                    <el-icon><Phone /></el-icon>
                                </el-button>
                            </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="取件码" v-if="logInfo.pickup_code">
                            <div class="flex items-center space-x-2">
                                <span class="font-mono text-lg">{{ logInfo.pickup_code }}</span>
                                <el-button type="primary" link size="small" @click="copyText(logInfo.pickup_code)">
                                    <el-icon><CopyDocument /></el-icon>
                                </el-button>
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>
            </div>

            <!-- 右侧回调内容 -->
            <div class="space-y-4">
                <!-- 回调原始内容 -->
                <el-card class="box-card !border-none" shadow="never" v-if="logInfo.content">
                    <template #header>
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium">回调内容</h3>
                            <el-button type="primary" link size="small" @click="copyText(logInfo.content)">
                                <el-icon><CopyDocument /></el-icon>
                                复制
                            </el-button>
                        </div>
                    </template>
                    
                    <!-- 格式化的JSON内容 -->
                    <div v-if="logInfo.content_formatted && typeof logInfo.content_formatted === 'object'">
                        <el-tree
                            :data="formatJsonForTree(logInfo.content_formatted)"
                            :props="{ children: 'children', label: 'label' }"
                            default-expand-all
                            class="json-tree"
                        />
                    </div>
                    
                    <!-- 原始内容 -->
                    <div v-else class="bg-gray-50 p-4 rounded">
                        <pre class="text-sm whitespace-pre-wrap">{{ logInfo.content }}</pre>
                    </div>
                </el-card>

                <!-- 操作按钮 -->
                <el-card class="box-card !border-none" shadow="never">
                    <template #header>
                        <h3 class="text-lg font-medium">操作</h3>
                    </template>
                    
                    <div class="space-y-2">
                        <el-button type="primary" @click="viewOrder(logInfo.order_id)" class="w-full">
                            <el-icon><View /></el-icon>
                            查看关联订单
                        </el-button>
                        <el-button type="danger" @click="deleteLog()" class="w-full">
                            <el-icon><Delete /></el-icon>
                            删除此日志
                        </el-button>
                    </div>
                </el-card>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { 
    Refresh, 
    CopyDocument, 
    Phone, 
    View, 
    Delete 
} from '@element-plus/icons-vue'
import { getExpressLogInfo, deleteExpressLog } from '@/addon/yz_she/api/express-log'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const logInfo = ref<any>({})

/**
 * 加载物流日志详情
 */
const loadLogInfo = () => {
    loading.value = true
    const id = parseInt(route.params.id as string)
    
    getExpressLogInfo(id).then(res => {
        loading.value = false
        logInfo.value = res.data
    }).catch(() => {
        loading.value = false
    })
}

/**
 * 刷新详情
 */
const refreshDetail = () => {
    loadLogInfo()
}

/**
 * 返回列表
 */
const goBack = () => {
    router.go(-1)
}

/**
 * 复制文本
 */
const copyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

/**
 * 拨打电话
 */
const callPhone = (phone: string) => {
    window.open(`tel:${phone}`)
}

/**
 * 查看关联订单
 */
const viewOrder = (orderId: number) => {
    router.push(`/yz_she/recycle-order/detail/${orderId}`)
}

/**
 * 删除日志
 */
const deleteLog = () => {
    ElMessageBox.confirm('确定要删除这条物流日志吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteExpressLog(logInfo.value.id).then(() => {
            ElMessage.success('删除成功')
            goBack()
        })
    })
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (typeCode: number) => {
    const tagTypes: Record<number, string> = {
        1: 'warning',   // 待揽收
        2: 'primary',   // 运输中
        3: 'success',   // 已签收
        4: 'danger',    // 拒收退回
        99: 'info'      // 已取消
    }
    return tagTypes[typeCode] || 'info'
}

/**
 * 是否有重量信息
 */
const hasWeightInfo = computed(() => {
    return logInfo.value.weight > 0 || 
           logInfo.value.real_weight > 0 || 
           logInfo.value.transfer_weight > 0 || 
           logInfo.value.cal_weight > 0 || 
           logInfo.value.volume > 0 || 
           logInfo.value.parse_weight > 0
})

/**
 * 是否有费用信息
 */
const hasFeeInfo = computed(() => {
    return logInfo.value.total_freight > 0 || 
           logInfo.value.freight > 0 || 
           logInfo.value.freight_insured > 0 || 
           logInfo.value.freight_haocai > 0 || 
           logInfo.value.change_bill || 
           logInfo.value.change_bill_freight > 0
})

/**
 * 是否有快递员信息
 */
const hasCourierInfo = computed(() => {
    return logInfo.value.courier_name || 
           logInfo.value.courier_phone || 
           logInfo.value.pickup_code
})

/**
 * 格式化JSON为树形结构
 */
const formatJsonForTree = (obj: any, parentKey = ''): any[] => {
    const result: any[] = []
    
    for (const [key, value] of Object.entries(obj)) {
        const node: any = {
            label: `${key}: ${typeof value === 'object' ? '' : value}`,
            children: []
        }
        
        if (typeof value === 'object' && value !== null) {
            node.children = formatJsonForTree(value, key)
        }
        
        result.push(node)
    }
    
    return result
}

onMounted(() => {
    loadLogInfo()
})
</script>

<style lang="scss" scoped>
.json-tree {
    :deep(.el-tree-node__content) {
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }
}
</style>
