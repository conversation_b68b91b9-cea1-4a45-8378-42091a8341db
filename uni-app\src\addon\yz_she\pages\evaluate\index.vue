<template>
  <view class="evaluate-page">
    <!-- 顶部搜索栏 -->
    <view class="search-section">
      <view class="search-container">
        <view class="search-box">
          <view class="search-icon">
            <svg viewBox="0 0 1024 1024" width="20" height="20">
              <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" fill="currentColor"/>
            </svg>
          </view>
          <input
            class="search-input"
            type="text"
            placeholder="输入货号（一步估价，立即实现）"
            v-model="searchKeyword"
            @input="onSearchInput"
            @confirm="onSearch"
          />
          <view v-if="searchKeyword" class="clear-icon" @click="clearSearchKeyword">✕</view>
        </view>
        <view class="search-button" @click="onSearch">搜索</view>
      </view>
    </view>

    <!-- 搜索记录区域 -->
    <view class="search-history-section" v-if="searchKeyword.trim() && (searchHistory.length > 0 || searchResults.length > 0)">
      <!-- 搜索历史 -->
      <view class="history-container" v-if="searchHistory.length > 0">
        <view class="history-header">
          <text class="history-title">搜索历史</text>
          <view class="clear-history" @click="clearSearchHistory">
            <text class="clear-text">清空</text>
          </view>
        </view>
        <view class="history-tags">
          <view
            class="history-tag"
            v-for="(item, index) in searchHistory"
            :key="index"
            @click="selectHistoryItem(item)"
          >
            <text class="tag-text">{{ item }}</text>
          </view>
        </view>
      </view>

      <!-- 搜索结果记录 -->
      <view class="search-results-container" v-if="searchResults.length > 0">
        <view class="results-header">
          <text class="results-title">搜索到的商品 ({{ searchResults.length }})</text>
        </view>
        <view class="results-list">
          <view
            class="result-item"
            v-for="item in searchResults"
            :key="item.id"
            @click="selectItem(item)"
          >
            <view class="result-image">
              <image
                class="product-image"
                :src="item.image ? img(item.image) : img('static/resource/images/diy/shop_default.jpg')"
                mode="aspectFill"
              />
            </view>
            <view class="result-info">
              <text class="result-name">{{ item.name }}</text>
              <text class="result-code">{{ item.code }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 如何查找货号提示 -->
    <view class="help-section" v-show="!searchKeyword.trim()">
      <view class="help-content">
        <text class="help-text">如何查找货号</text>
        <view class="help-icon" @click="showHelpModal">
          <text class="icon-text">?</text>
        </view>
      </view>
    </view>

    <!-- 热门回收横向滚动区域 -->
    <view class="hot-recycle-horizontal" v-if="false">
      <view class="section-header">
        <text class="section-title">热门回收</text>
        <text class="section-subtitle">快速选择热门商品</text>
      </view>
      <scroll-view class="hot-scroll" scroll-x="true" show-scrollbar="false">
        <view class="hot-items" v-if="hotRecycleItems.length > 0">
          <view
            class="hot-item"
            v-for="item in hotRecycleItems"
            :key="item.id"
            @click="selectItem(item)"
          >
            <view class="hot-item-image">
              <image
                class="hot-product-image"
                :src="item.image ? img(item.image) : img('static/resource/images/diy/shop_default.jpg')"
                mode="aspectFill"
              />
            </view>
            <view class="hot-item-info">
              <text class="hot-item-name">{{ item.name }}</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <text class="empty-text">暂无热门商品</text>
        </view>
      </scroll-view>
    </view>

    <!-- 全部回收商品列表 -->
    <view class="all-recycle-section">
      <view class="section-header">
        <text class="section-title">全部回收</text>
        <text class="section-subtitle">{{ allRecycleItems.length }}个商品</text>
      </view>
      <view class="recycle-list" v-if="allRecycleItems.length > 0">
        <view
          class="recycle-item"
          v-for="item in allRecycleItems"
          :key="item.id"
          @click="selectItem(item)"
        >
          <view class="item-image">
            <image
              class="product-image"
              :src="item.image ? img(item.image) : img('static/resource/images/diy/shop_default.jpg')"
              mode="aspectFill"
            />
          </view>
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-code">{{ item.code }}</text>
          </view>
          <view class="item-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
      <view v-else class="empty-state">
        <text class="empty-text">暂无商品数据</text>
        <text class="empty-hint">请先选择品牌或添加商品数据</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-action">
      <view class="action-buttons">
        <view class="no-code-button" @click="showHelpModal">
          <text class="no-code-text">找不到货号点这里</text>
        </view>
        <view class="camera-button" @click="goToPhotoEvaluate">
          <view class="camera-icon">
            <svg viewBox="0 0 1024 1024" width="20" height="20">
              <path d="M864 260H728l-32.4-90.8a32.07 32.07 0 0 0-30.2-21.2H358.6c-13.5 0-25.6 8.5-30.1 21.2L296 260H160c-44.2 0-80 35.8-80 80v456c0 44.2 35.8 80 80 80h704c44.2 0 80-35.8 80-80V340c0-44.2-35.8-80-80-80zM512 716c-88.4 0-160-71.6-160-160s71.6-160 160-160 160 71.6 160 160-71.6 160-160 160zm0-256c-53 0-96 43-96 96s43 96 96 96 96-43 96-96-43-96-96-96z" fill="currentColor"/>
            </svg>
          </view>
          <text class="camera-text">拍照估价</text>
        </view>
      </view>
    </view>

    <!-- 购物车图标 -->
    <view class="cart-icon" @click="goToCart">
      <view class="cart-badge">{{ cartCount }}</view>
      <svg viewBox="0 0 1024 1024" width="24" height="24">
        <path d="M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12.1 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 0 0-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-17.4-28-34.6-28H96.5a35.3 35.3 0 1 0 0 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 0 0-3 36.8c6.1 11.9 18.4 19.4 31.5 19.4h62.8a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 0 0-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 0 0-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 0 1-31.6 31.6z" fill="currentColor"/>
      </svg>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getHotRecycleItems, getAllRecycleItems, searchGoodsByCode, getBrandHotGoods, getBrandAllGoods, searchBrandGoods } from '@/addon/yz_she/api/brand'
import { img } from '@/utils/common'
import useMemberStore from '@/stores/member'
import { useLogin } from '@/hooks/useLogin'

// 响应式数据
const memberStore = useMemberStore()
const userInfo = computed(() => memberStore.info)
const searchKeyword = ref('')
const brandInfo = ref({ id: '', name: '' })
const cartCount = ref(0)
let searchTimer = null

// 搜索记录相关数据
const searchHistory = ref([])
const searchResults = ref([])

// 热门回收商品数据（横向显示）
const hotRecycleItems = ref([])

// 全部回收商品数据（列表显示）
const allRecycleItems = ref([])

// 方法
const onSearchInput = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 当搜索框为空时，立即重新加载原始数据
  if (!searchKeyword.value || searchKeyword.value.trim() === '') {
    loadAllRecycleItems()
    return
  }

  // 设置防抖定时器，500ms后执行搜索
  searchTimer = setTimeout(() => {
    performSearch()
  }, 500)
}

const onSearch = () => {
  // 清除防抖定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  if (!searchKeyword.value.trim()) {
    loadAllRecycleItems()
    return
  }

  // 立即执行搜索
  performSearch()
}

const performSearch = async () => {
  try {
    uni.showLoading({
      title: '搜索中...'
    })

    let result

    // 如果有品牌信息，使用品牌专用搜索接口
    if (brandInfo.value && brandInfo.value.id) {
      result = await searchBrandGoods(brandInfo.value.id, searchKeyword.value)
    } else {
      // 没有品牌信息，使用通用搜索接口
      result = await searchGoodsByCode(searchKeyword.value)
    }

    uni.hideLoading()

    // 添加到搜索历史
    addToSearchHistory(searchKeyword.value)

    // 修复数据结构判断：API返回的数据在 result.data.data 中
    if (result.code === 1 && result.data && result.data.data && result.data.data.length > 0) {
      // 找到商品，显示在搜索结果区域
      const foundItems = result.data.data
      searchResults.value = foundItems

      // 将搜索结果也显示在全部回收区域
      allRecycleItems.value = foundItems

      const brandText = brandInfo.value ? `在${brandInfo.value.name}品牌中` : ''
      uni.showToast({
        title: `${brandText}找到 ${foundItems.length} 个商品`,
        icon: 'success'
      })
    } else {
      // 没有找到商品，清空搜索结果
      searchResults.value = []

      const brandText = brandInfo.value ? `${brandInfo.value.name}品牌中` : ''
      uni.showToast({
        title: `${brandText}未找到该货号商品`,
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    searchResults.value = []
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  }
}

const showHelpModal = () => {
  uni.showModal({
    title: '如何查找货号',
    content: '货号通常位于鞋盒标签、鞋舌标签或鞋垫下方。您也可以拍照上传，我们的专业评估师会帮您识别。',
    showCancel: false,
    confirmText: '知道了'
  })
}

const selectItem = (item: any) => {
  console.log('选择商品:', item)

  // 检查是否登录
  if (!userInfo.value) {
    console.log('用户未登录，跳转到登录页面')

    // 获取当前页面的完整参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    console.log('selectItem - 保存的页面参数:', options)

    useLogin().setLoginBack({
      url: '/addon/yz_she/pages/evaluate/index',
      param: options
    })
    return false
  }

  // 跳转到评估详情页面，只传递id参数
  uni.navigateTo({
    url: `/addon/yz_she/pages/evaluate/detail?id=${item.id}`
  })
}

// 跳转到拍照估价页面
const goToPhotoEvaluate = () => {
  console.log('从index页面跳转到拍照估价')

  // 检查是否登录
  if (!userInfo.value) {
    console.log('用户未登录，跳转到登录页面')

    // 获取当前页面的完整参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    console.log('goToPhotoEvaluate - 保存的页面参数:', options)

    useLogin().setLoginBack({
      url: '/addon/yz_she/pages/evaluate/index',
      param: options
    })
    return false
  }

  // 获取品牌信息（从商品数据中提取）
  let brandInfo = null

  // 优先从热门商品获取品牌信息
  if (hotRecycleItems.value.length > 0) {
    const firstItem = hotRecycleItems.value[0]
    if (firstItem.brand) {
      brandInfo = firstItem.brand
    }
  }

  // 如果热门商品没有品牌信息，从全部商品获取
  if (!brandInfo && allRecycleItems.value.length > 0) {
    const firstItem = allRecycleItems.value[0]
    if (firstItem.brand) {
      brandInfo = firstItem.brand
    }
  }


  console.log('使用品牌信息:', brandInfo)

  // 将数据存储到全局存储中，避免URL参数长度限制
  const jumpData = {
    from: 'index',
    brandId: brandInfo.id,
    brandName: brandInfo.name || '',
    brandLogo: brandInfo.logo || '',
    categoryId: brandInfo.category_id || ''
  }

  console.log('存储跳转数据:', jumpData)

  // 存储到本地缓存
  uni.setStorageSync('photoEvaluateData', jumpData)

  // 简单跳转，不携带复杂参数
  uni.navigateTo({
    url: '/addon/yz_she/pages/evaluate/photo?from=index'
  })
}

// 删除拍照功能，"找不到货号点这里"现在直接显示帮助信息

const handleImageUpload = (imagePath: string) => {
  uni.showLoading({
    title: '识别中...'
  })
  
  // 这里可以调用图片识别API
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '识别完成',
      icon: 'success'
    })
  }, 2000)
}

const goToCart = () => {
  uni.navigateTo({
    url: '/addon/shop/pages/goods/cart'
  })
}

// 加载热门回收商品
const loadHotRecycleItems = async () => {
  try {
    let result

    // 如果有品牌信息，使用品牌专用API
    if (brandInfo.value && brandInfo.value.id) {
      result = await getBrandHotGoods(brandInfo.value.id, { limit: 6 })
    } else {
      const params = {
        limit: 6,
        is_hot: 1,
        status: 1
      }
      result = await getHotRecycleItems(params)
    }

    if (result.code === 1) {
      // API返回的是分页数据结构 {data: {data: [...], total: ...}}
      hotRecycleItems.value = result.data.data || []
    }
  } catch (error) {
    console.error('加载热门回收商品失败:', error)
  }
}

// 加载全部回收商品
const loadAllRecycleItems = async () => {
  try {
    let result

    // 如果有品牌信息，使用品牌专用API
    if (brandInfo.value && brandInfo.value.id) {
      result = await getBrandAllGoods(brandInfo.value.id, { limit: 20 })
    } else {
      const params = {
        limit: 20,
        status: 1
      }
      result = await getAllRecycleItems(params)
    }

    if (result.code === 1) {
      // API返回的是分页数据结构 {data: {data: [...], total: ...}}
      allRecycleItems.value = result.data.data || []
    }
  } catch (error) {
    console.error('加载全部回收商品失败:', error)
  }
}

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.brandId && options.brandName) {
    brandInfo.value = {
      id: parseInt(options.brandId),
      name: decodeURIComponent(options.brandName)
    }
  }
})

onMounted(async () => {
  // 加载搜索历史
  loadSearchHistory()

  // 加载数据（会根据brandInfo自动筛选）
  await Promise.all([
    loadHotRecycleItems(),
    loadAllRecycleItems()
  ])
})

// 监听搜索框内容变化
watch(searchKeyword, (newValue) => {
  // 当搜索框为空时，重新加载原始数据并清空搜索结果
  if (!newValue || newValue.trim() === '') {
    searchResults.value = []
    loadAllRecycleItems()
  }
})



// 清除搜索关键词
const clearSearchKeyword = () => {
  searchKeyword.value = ''
  searchResults.value = []
  // 重新加载原始数据
  loadAllRecycleItems()
}

// 搜索记录相关方法
const addToSearchHistory = (keyword) => {
  if (!keyword || keyword.trim() === '') return

  const trimmedKeyword = keyword.trim()

  // 移除已存在的相同关键词
  const index = searchHistory.value.indexOf(trimmedKeyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }

  // 添加到开头
  searchHistory.value.unshift(trimmedKeyword)

  // 限制历史记录数量为10条
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }

  // 保存到本地存储
  saveSearchHistory()
}

const clearSearchHistory = () => {
  searchHistory.value = []
  uni.removeStorageSync('evaluate_search_history')
  uni.showToast({
    title: '已清空搜索历史',
    icon: 'success'
  })
}

const selectHistoryItem = (keyword) => {
  searchKeyword.value = keyword
  performSearch()
}

const saveSearchHistory = () => {
  try {
    uni.setStorageSync('evaluate_search_history', searchHistory.value)
  } catch (error) {
    console.error('保存搜索历史失败:', error)
  }
}

const loadSearchHistory = () => {
  try {
    const history = uni.getStorageSync('evaluate_search_history')
    if (history && Array.isArray(history)) {
      searchHistory.value = history
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.evaluate-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

// 搜索区域
.search-section {
  background-color: #fff;
  padding: 20rpx 24rpx;
  margin: 0;
  border-radius: 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .search-container {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 50rpx;
      padding: 0 24rpx;
      height: 80rpx;
      border: 1rpx solid #e8e8e8;
      transition: all 0.3s ease;

      &:focus-within {
        border-color: #333;
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .search-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        color: #333;

        svg {
          width: 32rpx;
          height: 32rpx;

          path {
            stroke-width: 2;
            font-weight: bold;
          }
        }
      }

      .search-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
        color: #333;
        background: transparent;

        &::placeholder {
          color: #999;
        }
      }

      .clear-icon {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #999;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        margin-right: 16rpx;
        transition: all 0.3s ease;

        &:active {
          background-color: rgba(0, 0, 0, 0.2);
          transform: scale(0.9);
        }
      }
    }

    .search-button {
      background-color: transparent;
      color: #333;
      padding: 8rpx 20rpx;
      border-radius: 40rpx;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.7;
        transform: scale(0.95);
      }
    }
  }
}

// 搜索记录区域
.search-history-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .history-container {
    padding: 24rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .history-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .history-title {
        font-size: 26rpx;
        font-weight: 600;
        color: #333;
      }

      .clear-history {
        padding: 8rpx 16rpx;
        background-color: #f5f5f5;
        border-radius: 20rpx;
        transition: all 0.3s ease;

        &:active {
          background-color: #e8e8e8;
          transform: scale(0.95);
        }

        .clear-text {
          font-size: 22rpx;
          color: #666;
        }
      }
    }

    .history-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .history-tag {
        padding: 8rpx 16rpx;
        background-color: #f8f9fa;
        border-radius: 20rpx;
        border: 1rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.95);
        }

        .tag-text {
          font-size: 24rpx;
          color: #495057;
        }
      }
    }
  }

  .search-results-container {
    padding: 24rpx;

    .results-header {
      margin-bottom: 16rpx;

      .results-title {
        font-size: 26rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .results-list {
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .result-item {
        display: flex;
        align-items: center;
        gap: 16rpx;
        padding: 12rpx;
        background-color: #f8f9fa;
        border-radius: 12rpx;
        transition: all 0.3s ease;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.98);
        }

        .result-image {
          width: 60rpx;
          height: 60rpx;
          border-radius: 8rpx;
          overflow: hidden;
          background-color: #fff;
          border: 1rpx solid #e0e0e0;

          .product-image {
            width: 100%;
            height: 100%;
          }
        }

        .result-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4rpx;

          .result-name {
            font-size: 24rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .result-code {
            font-size: 22rpx;
            color: #999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          }
        }
      }
    }
  }
}

// 帮助区域
.help-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .help-content {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .help-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .help-icon {
      width: 34rpx;
      height: 34rpx;
      background-color: transparent;
      border: 2rpx solid #ccc;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      .icon-text {
        font-size: 20rpx;
        color: #999;
        font-weight: 600;
      }

      &:active {
        border-color: #999;
        transform: scale(1.05);
      }
    }
  }
}

// 热门回收横向滚动区域
.hot-recycle-horizontal {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-header {
    padding: 30rpx 24rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1a1a1a;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #999;
    }
  }

  .hot-scroll {
    padding-bottom: 30rpx;

    .hot-items {
      display: flex;
      gap: 20rpx;
      padding: 0 24rpx;

      .hot-item {
        flex-shrink: 0;
        width: 160rpx;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
        }

        .hot-item-image {
          width: 100%;
          height: 120rpx;
          border-radius: 12rpx 12rpx 0 0;
          overflow: hidden;
          background-color: #f8f9fa;

          .hot-product-image {
            width: 100%;
            height: 100%;
          }
        }

        .hot-item-info {
          padding: 16rpx;

          .hot-item-name {
            display: block;
            font-size: 24rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// 全部回收区域
.all-recycle-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-header {
    padding: 30rpx 24rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1a1a1a;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #999;
    }
  }

  .recycle-list {
    padding: 0 24rpx 30rpx;

    .recycle-item {
      display: flex;
      align-items: center;
      gap: 16rpx;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      transition: all 0.3s ease;
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f8f9fa;
        transform: scale(0.98);
      }

      .item-image {
        width: 100rpx;
        height: 100rpx;
        border-radius: 16rpx;
        overflow: hidden;
        background-color: #f8f9fa;
        border: 1rpx solid #e0e0e0;

        .product-image {
          width: 100%;
          height: 100%;
        }
      }

      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6rpx;

        .item-name {
          font-size: 24rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .item-code {
          font-size: 24rpx;
          color: #999;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          font-weight: 400;
          margin-top: 6rpx;
          letter-spacing: 0.5rpx;
          opacity: 0.8;
          background: rgba(0, 0, 0, 0.03);
          padding: 4rpx 8rpx;
          border-radius: 6rpx;
          display: inline-block;
          line-height: 1.2;
        }
      }

      .item-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
        height: 40rpx;

        .arrow-icon {
          font-size: 32rpx;
          color: #ccc;
          font-weight: 300;
        }
      }
    }
  }
}

// 底部操作区域
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 20rpx 24rpx;
  box-shadow: 0 -4rpx 24rpx rgba(44, 62, 80, 0.3);
  z-index: 100;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);

  .action-buttons {
    display: flex;
    gap: 20rpx;
    align-items: center;

    .no-code-button {
      flex: 1;
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
      padding: 15rpx 12rpx;
      text-align: left;
      font-size: 24rpx;
      font-weight: 400;
      transition: all 0.3s ease;
      position: relative;

      &:active {
        color: rgba(255, 255, 255, 1);
      }

      .no-code-text {
        display: block;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;

        &::after {
          content: '→';
          margin-left: 8rpx;
          font-size: 20rpx;
          opacity: 0.6;
        }
      }
    }

    .camera-button {
      flex: 1.2;
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      color: #fff;
      padding: 16rpx 24rpx;
      border-radius: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-size: 30rpx;
      font-weight: 700;
      transition: all 0.3s ease;
      box-shadow: 0 6rpx 24rpx rgba(22, 160, 133, 0.5);
      position: relative;
      overflow: hidden;

      // 添加光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:active {
        transform: scale(0.96);
        background: linear-gradient(135deg, #138d75 0%, #17a2b8 100%);

        &::before {
          left: 100%;
        }
      }

      .camera-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          width: 36rpx;
          height: 36rpx;
          color: #fff;
        }
      }

      .camera-text {
        display: block;
        color: #fff;
        font-weight: 700;
        text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
        letter-spacing: 1rpx;
      }
    }
  }
}

// 购物车图标
.cart-icon {
  position: fixed;
  bottom: 140rpx;
  right: 24rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 99;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
  }

  .cart-badge {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    background-color: #ff4757;
    color: #fff;
    font-size: 20rpx;
    font-weight: 600;
    padding: 2rpx 6rpx;
    border-radius: 50rpx;
    min-width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  svg {
    width: 56rpx;
    height: 56rpx;
    color: #333;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 16rpx;
  }

  .empty-hint {
    font-size: 24rpx;
    color: #ccc;
  }
}
</style>
