<?php

return  [
    [
        'menu_name' => '柚子奢侈品回收',
        'menu_key' => 'yz_she',
        'menu_type' => 0,
        'icon' => '',
        'api_url' => '',
        'router_path' => '',
        'view_path' => '',
        'methods' => '',
        'sort' => 100,
        'status' => 1,
        'is_show' => 1,
        'children' => [
            [
                'menu_name' => '柚子奢侈品回收',
                'menu_key' => 'yz_she_hello_world',
                'menu_type' => 1,
                'icon' => '',
                'api_url' => 'yz_she/hello_world',
                'router_path' => 'yz_she/hello_world',
                'view_path' => 'hello_world/index',
                'methods' => 'get',
                'sort' => 100,
                'status' => 1,
                'is_show' => 1,
                'children' => []
            ],
            [
                'menu_name' => '商品管理',
                'menu_key' => 'yz_she_goods',
                'menu_type' => 0,
                'icon' => 'iconfont-iconshangpinliebiao',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 90,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '商品分类',
                        'menu_key' => 'yz_she_goods_category',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/goods/category',
                        'router_path' => 'yz_she/goods/category',
                        'view_path' => 'goods/category_list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '商品品牌',
                        'menu_key' => 'yz_she_goods_brand',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/goods/brand',
                        'router_path' => 'yz_she/goods/brand',
                        'view_path' => 'goods/brand_list',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '商品列表',
                        'menu_key' => 'yz_she_goods_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/goods',
                        'router_path' => 'yz_she/goods/list',
                        'view_path' => 'goods/goods_list',
                        'methods' => 'get',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '加价券管理',
                'menu_key' => 'yz_she_voucher',
                'menu_type' => 0,
                'icon' => 'iconfont-iconjiajiaquan',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 80,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '加价券列表',
                        'menu_key' => 'yz_she_voucher_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/voucher',
                        'router_path' => 'yz_she/voucher/list',
                        'view_path' => 'voucher/list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加加价券',
                        'menu_key' => 'yz_she_voucher_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/voucher',
                        'router_path' => 'yz_she/voucher/add',
                        'view_path' => 'voucher/add',
                        'methods' => 'post',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑加价券',
                        'menu_key' => 'yz_she_voucher_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/voucher/:id',
                        'router_path' => 'yz_she/voucher/edit/:id',
                        'view_path' => 'voucher/edit',
                        'methods' => 'put',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],

                ]
            ],
            [
                'menu_name' => '回收标准管理',
                'menu_key' => 'yz_she_recycle_standard',
                'menu_type' => 0,
                'icon' => 'iconfont-iconbiaozhun',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 70,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '回收标准列表',
                        'menu_key' => 'yz_she_recycle_standard_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard',
                        'router_path' => 'yz_she/recycle_standard/list',
                        'view_path' => 'recycle_standard/list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加回收标准',
                        'menu_key' => 'yz_she_recycle_standard_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard',
                        'router_path' => 'yz_she/recycle_standard/add',
                        'view_path' => 'recycle_standard/add',
                        'methods' => 'post',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑回收标准',
                        'menu_key' => 'yz_she_recycle_standard_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard/:id',
                        'router_path' => 'yz_she/recycle_standard/edit/:id',
                        'view_path' => 'recycle_standard/edit',
                        'methods' => 'put',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除回收标准',
                        'menu_key' => 'yz_she_recycle_standard_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '回收标准状态更新',
                        'menu_key' => 'yz_she_recycle_standard_status',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_standard/status/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '分类配置管理',
                'menu_key' => 'yz_she_category_config',
                'menu_type' => 0,
                'icon' => 'iconfont-iconpeizhi',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 60,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '配置管理',
                        'menu_key' => 'yz_she_category_config_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/config',
                        'router_path' => 'yz_she/category/config',
                        'view_path' => 'category/config',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取分类选项',
                        'menu_key' => 'yz_she_category_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取照片配置列表',
                        'menu_key' => 'yz_she_category_photos_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取照片配置详情',
                        'menu_key' => 'yz_she_category_photos_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 75,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加照片配置',
                        'menu_key' => 'yz_she_category_photos_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑照片配置',
                        'menu_key' => 'yz_she_category_photos_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 65,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除照片配置',
                        'menu_key' => 'yz_she_category_photos_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取配件配置列表',
                        'menu_key' => 'yz_she_category_accessories_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 55,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取配件配置详情',
                        'menu_key' => 'yz_she_category_accessories_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加配件配置',
                        'menu_key' => 'yz_she_category_accessories_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 45,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑配件配置',
                        'menu_key' => 'yz_she_category_accessories_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 40,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除配件配置',
                        'menu_key' => 'yz_she_category_accessories_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 35,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '估价订单管理',
                'menu_key' => 'yz_she_quote_order',
                'menu_type' => 0,
                'icon' => 'iconfont-icondingdan',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 50,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '估价订单列表',
                        'menu_key' => 'yz_she_quote_order_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/lists',
                        'router_path' => 'yz_she/quote-order',
                        'view_path' => 'quote-order/list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '估价订单详情',
                        'menu_key' => 'yz_she_quote_order_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/info/:id',
                        'router_path' => 'yz_she/quote-order/detail/:id',
                        'view_path' => 'quote-order/detail',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '估价操作',
                        'menu_key' => 'yz_she_quote_order_quote',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/quote/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '取消订单',
                        'menu_key' => 'yz_she_quote_order_cancel',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/cancel/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '完成订单',
                        'menu_key' => 'yz_she_quote_order_complete',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/complete/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 65,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],

                    [
                        'menu_name' => '获取状态选项',
                        'menu_key' => 'yz_she_quote_order_status_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/get_status_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '批量操作',
                        'menu_key' => 'yz_she_quote_order_batch',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/batch',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 40,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '导出数据',
                        'menu_key' => 'yz_she_quote_order_export',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/quote_order/export',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 30,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '回收订单管理',
                'menu_key' => 'yz_she_recycle_order',
                'menu_type' => 0,
                'icon' => 'iconfont-iconhuishou',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 45,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '回收订单列表',
                        'menu_key' => 'yz_she_recycle_order_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/lists',
                        'router_path' => 'yz_she/recycle-order',
                        'view_path' => 'recycle-order/list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '回收订单详情',
                        'menu_key' => 'yz_she_recycle_order_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/info/:id',
                        'router_path' => 'yz_she/recycle-order/detail/:id',
                        'view_path' => 'recycle-order/detail',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '收货确认',
                        'menu_key' => 'yz_she_recycle_order_receive',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/receive/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '开始质检',
                        'menu_key' => 'yz_she_recycle_order_start_quality',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/start_quality/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '完成质检',
                        'menu_key' => 'yz_she_recycle_order_complete_quality',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/complete_quality/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '结算订单',
                        'menu_key' => 'yz_she_recycle_order_settlement',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/settlement/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '退回订单',
                        'menu_key' => 'yz_she_recycle_order_return',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/return/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 40,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取状态选项',
                        'menu_key' => 'yz_she_recycle_order_status_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/get_status_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 30,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取订单来源选项',
                        'menu_key' => 'yz_she_recycle_order_source_type_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/get_source_type_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 25,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取配送方式选项',
                        'menu_key' => 'yz_she_recycle_order_delivery_type_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/get_delivery_type_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 20,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '批量操作',
                        'menu_key' => 'yz_she_recycle_order_batch',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/batch',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 15,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '导出数据',
                        'menu_key' => 'yz_she_recycle_order_export',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/recycle_order/export',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 10,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '物流管理',
                'menu_key' => 'yz_she_express',
                'menu_type' => 0,
                'icon' => 'iconfont-iconwuliu',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 42,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '物流回调日志',
                        'menu_key' => 'yz_she_express_log_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/lists',
                        'router_path' => 'yz_she/express_log',
                        'view_path' => 'express_log/list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '物流日志详情',
                        'menu_key' => 'yz_she_express_log_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/info/:id',
                        'router_path' => 'yz_she/express_log/detail/:id',
                        'view_path' => 'express_log/detail',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除物流日志',
                        'menu_key' => 'yz_she_express_log_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '批量删除物流日志',
                        'menu_key' => 'yz_she_express_log_batch_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/batch',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '导出物流日志',
                        'menu_key' => 'yz_she_express_log_export',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/export',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取状态码选项',
                        'menu_key' => 'yz_she_express_log_type_code_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/get_type_code_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取扣费状态选项',
                        'menu_key' => 'yz_she_express_log_fee_over_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/get_fee_over_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 40,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取统计数据',
                        'menu_key' => 'yz_she_express_log_statistics',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/get_statistics',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 30,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '根据运单号获取最新日志',
                        'menu_key' => 'yz_she_express_log_latest_by_waybill',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/get_latest_by_waybill',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 20,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '根据订单ID获取物流日志',
                        'menu_key' => 'yz_she_express_log_by_order_id',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/express_log/get_by_order_id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 10,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '会员管理',
                'menu_key' => 'yz_she_member',
                'menu_type' => 0,
                'icon' => 'iconfont-iconhuiyuan',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 40,
                'status' => 1,
                'is_show' => 1,
                'children' => [
                    [
                        'menu_name' => '会员收货地址',
                        'menu_key' => 'yz_she_member_address_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address',
                        'router_path' => 'yz_she/member/address_list',
                        'view_path' => 'member/address_list',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 1,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取会员地址详情',
                        'menu_key' => 'yz_she_member_address_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加会员地址',
                        'menu_key' => 'yz_she_member_address_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑会员地址',
                        'menu_key' => 'yz_she_member_address_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除会员地址',
                        'menu_key' => 'yz_she_member_address_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取会员选项',
                        'menu_key' => 'yz_she_member_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/member/address/member_options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ],
            [
                'menu_name' => '分类配置API权限',
                'menu_key' => 'yz_she_category_config_api',
                'menu_type' => 0,
                'icon' => '',
                'api_url' => '',
                'router_path' => '',
                'view_path' => '',
                'methods' => '',
                'sort' => 59,
                'status' => 1,
                'is_show' => 0,
                'children' => [
                    [
                        'menu_name' => '获取照片配置列表',
                        'menu_key' => 'yz_she_category_photos_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 100,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取照片配置详情',
                        'menu_key' => 'yz_she_category_photos_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 90,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加照片配置',
                        'menu_key' => 'yz_she_category_photos_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 80,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑照片配置',
                        'menu_key' => 'yz_she_category_photos_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 70,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除照片配置',
                        'menu_key' => 'yz_she_category_photos_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/photos/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 60,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取配件配置列表',
                        'menu_key' => 'yz_she_category_accessories_list',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 50,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取配件配置详情',
                        'menu_key' => 'yz_she_category_accessories_info',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 40,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '添加配件配置',
                        'menu_key' => 'yz_she_category_accessories_add',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'post',
                        'sort' => 30,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '编辑配件配置',
                        'menu_key' => 'yz_she_category_accessories_edit',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'put',
                        'sort' => 20,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '删除配件配置',
                        'menu_key' => 'yz_she_category_accessories_del',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/accessories/:id',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'delete',
                        'sort' => 10,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ],
                    [
                        'menu_name' => '获取分类选项',
                        'menu_key' => 'yz_she_category_options',
                        'menu_type' => 1,
                        'icon' => '',
                        'api_url' => 'yz_she/category/options',
                        'router_path' => '',
                        'view_path' => '',
                        'methods' => 'get',
                        'sort' => 5,
                        'status' => 1,
                        'is_show' => 0,
                        'children' => []
                    ]
                ]
            ]
        ]
    ]
];
