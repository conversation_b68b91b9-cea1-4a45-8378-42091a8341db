<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\adminapi\controller\express;

use core\base\BaseAdminController;
use addon\yz_she\app\service\admin\express\ExpressLogService;

/**
 * 物流回调日志控制器
 * Class ExpressLog
 * @package addon\yz_she\app\adminapi\controller\express
 */
class ExpressLog extends BaseAdminController
{
    /**
     * 获取物流日志列表
     * @return \think\Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['waybill', ''],
            ['shopbill', ''],
            ['type_code', ''],
            ['fee_over', ''],
            ['courier_name', ''],
            ['courier_phone', ''],
            ['create_time', []]
        ]);
        
        return success((new ExpressLogService())->getPage($data));
    }

    /**
     * 物流日志详情
     * @param int $id
     * @return \think\Response
     */
    public function info(int $id)
    {
        return success((new ExpressLogService())->getInfo($id));
    }

    /**
     * 删除物流日志
     * @param int $id
     * @return \think\Response
     */
    public function del(int $id)
    {
        (new ExpressLogService())->del($id);
        return success('删除成功');
    }

    /**
     * 批量删除物流日志
     * @return \think\Response
     */
    public function batch()
    {
        $data = $this->request->params([
            ['ids', []]
        ]);

        if (empty($data['ids'])) {
            return fail('请选择要删除的数据');
        }

        (new ExpressLogService())->batchDel($data['ids']);
        return success('批量删除成功');
    }

    /**
     * 导出物流日志
     * @return \think\Response
     */
    public function export()
    {
        $data = $this->request->params([
            ['waybill', ''],
            ['shopbill', ''],
            ['type_code', ''],
            ['fee_over', ''],
            ['courier_name', ''],
            ['courier_phone', ''],
            ['create_time', []]
        ]);

        return success((new ExpressLogService())->export($data));
    }

    /**
     * 获取状态码选项
     * @return \think\Response
     */
    public function getTypeCodeOptions()
    {
        return success((new ExpressLogService())->getTypeCodeOptions());
    }

    /**
     * 获取扣费状态选项
     * @return \think\Response
     */
    public function getFeeOverOptions()
    {
        return success((new ExpressLogService())->getFeeOverOptions());
    }

    /**
     * 获取统计数据
     * @return \think\Response
     */
    public function getStatistics()
    {
        $data = $this->request->params([
            ['create_time', []]
        ]);

        return success((new ExpressLogService())->getStatistics($data));
    }

    /**
     * 根据运单号获取最新日志
     * @return \think\Response
     */
    public function getLatestByWaybill()
    {
        $waybill = $this->request->param('waybill', '');
        if (empty($waybill)) {
            return fail('运单号不能为空');
        }

        return success((new ExpressLogService())->getLatestByWaybill($waybill));
    }

    /**
     * 根据订单ID获取物流日志
     * @return \think\Response
     */
    public function getByOrderId()
    {
        $orderId = $this->request->param('order_id', 0);
        if (empty($orderId)) {
            return fail('订单ID不能为空');
        }

        return success((new ExpressLogService())->getByOrderId($orderId));
    }
}
