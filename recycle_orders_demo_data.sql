-- ========================================
-- 回收订单演示数据
-- 包含三种订单来源的完整演示数据
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 清空现有数据（可选）
-- ----------------------------
-- TRUNCATE TABLE `www_yz_she_recycle_orders`;
-- TRUNCATE TABLE `www_yz_she_recycle_order_logs`;

-- ----------------------------
-- 2. 插入演示分类数据（如果不存在）
-- ----------------------------
INSERT IGNORE INTO `www_shop_goods_category` (`category_id`, `category_name`, `pid`, `level`, `sort`, `is_show`, `create_time`, `update_time`) VALUES
(1, '手机数码', 0, 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '笔记本电脑', 0, 1, 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '平板电脑', 0, 1, 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '智能手表', 0, 1, 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 3. 插入演示品牌数据（如果不存在）
-- ----------------------------
INSERT IGNORE INTO `www_shop_brand` (`brand_id`, `brand_name`, `logo`, `sort`, `is_show`, `create_time`, `update_time`) VALUES
(1, '小米', 'https://via.placeholder.com/50x50/007bff/ffffff?text=MI', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '联想', 'https://via.placeholder.com/50x50/dc3545/ffffff?text=Lenovo', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '苹果', 'https://via.placeholder.com/50x50/000000/ffffff?text=Apple', 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '华为', 'https://via.placeholder.com/50x50/28a745/ffffff?text=HUAWEI', 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, '三星', 'https://via.placeholder.com/50x50/17a2b8/ffffff?text=Samsung', 5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 4. 插入演示商品数据（用于直接回收）
-- ----------------------------
INSERT IGNORE INTO `www_shop_goods` (`goods_id`, `goods_name`, `goods_cover`, `goods_category_id`, `brand_id`, `goods_code`, `price`, `market_price`, `is_show`, `create_time`, `update_time`) VALUES
(1001, '联想ThinkPad X1 Carbon 2023款', 'https://via.placeholder.com/200x200/28a745/ffffff?text=ThinkPad', 2, 2, 'TPX1C2023', 8500.00, 12000.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1002, 'MacBook Pro 13英寸 M1', 'https://via.placeholder.com/200x200/6c757d/ffffff?text=MacBook', 2, 3, 'MBP13M1', 9800.00, 13999.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1003, 'iPad Pro 12.9英寸', 'https://via.placeholder.com/200x200/000000/ffffff?text=iPad', 3, 3, 'IPADPRO129', 6800.00, 8999.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1004, 'Apple Watch Series 8', 'https://via.placeholder.com/200x200/000000/ffffff?text=Watch', 4, 3, 'AWS8', 2800.00, 3999.00, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 5. 插入演示会员数据（如果不存在）
-- ----------------------------
INSERT IGNORE INTO `www_member` (`member_id`, `username`, `nickname`, `mobile`, `headimg`, `create_time`, `update_time`) VALUES
(1001, 'user001', '张三', '13800138001', 'https://via.placeholder.com/60x60/007bff/ffffff?text=张', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1002, 'user002', '李四', '13800138002', 'https://via.placeholder.com/60x60/28a745/ffffff?text=李', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1003, 'user003', '王五', '13800138003', 'https://via.placeholder.com/60x60/dc3545/ffffff?text=王', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1004, 'user004', '赵六', '13800138004', 'https://via.placeholder.com/60x60/ffc107/ffffff?text=赵', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1005, 'user005', '钱七', '13800138005', 'https://via.placeholder.com/60x60/17a2b8/ffffff?text=钱', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 6. 插入演示地址数据
-- ----------------------------
INSERT IGNORE INTO `www_member_address` (`id`, `member_id`, `name`, `mobile`, `province_id`, `city_id`, `district_id`, `address`, `full_address`, `is_default`, `create_time`, `update_time`) VALUES
(1001, 1001, '张三', '13800138001', 110000, 110100, 110105, '建国路88号SOHO现代城A座1001室', '北京市北京市朝阳区建国路88号SOHO现代城A座1001室', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1002, 1002, '李四', '13800138002', 310000, 310100, 310115, '陆家嘴环路1000号恒生银行大厦20楼', '上海市上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1003, 1003, '王五', '13800138003', 440000, 440100, 440106, '珠江新城花城大道85号高德置地广场A座', '广东省广州市天河区珠江新城花城大道85号高德置地广场A座', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1004, 1004, '赵六', '13800138004', 440000, 440300, 440305, '科技园南区深南大道9988号', '广东省深圳市南山区科技园南区深南大道9988号', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1005, 1005, '钱七', '13800138005', 330000, 330100, 330106, '滨江区网商路699号阿里巴巴滨江园区', '浙江省杭州市滨江区网商路699号阿里巴巴滨江园区', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ----------------------------
-- 7. 插入演示估价订单数据（用于估价订单来源的回收订单）
-- ----------------------------
INSERT IGNORE INTO `www_yz_she_quote_orders` (`id`, `order_no`, `user_id`, `category_id`, `brand_id`, `product_name`, `status`, `admin_id`, `quote_price`, `quote_note`, `create_time`, `update_time`) VALUES
(2001, 'QT2024120001', 1001, 1, 1, '小米13 Ultra 512GB 黑色', 3, 1, 3200.00, '外观良好，功能正常，电池健康度85%', '2024-01-14 08:30:00', '2024-01-14 10:15:00'),
(2002, 'QT2024120002', 1004, 1, 3, 'iPhone 12 128GB 白色', 3, 1, 3300.00, '外观完好，功能正常，电池健康度92%，无维修记录', '2024-01-10 08:15:00', '2024-01-10 09:30:00'),
(2003, 'QT2024120003', 1005, 2, 4, 'MacBook Pro 14英寸 M2', 2, 1, 12800.00, '待用户确认估价', '2024-01-16 10:20:00', '2024-01-16 10:20:00');

-- ----------------------------
-- 8. 插入演示估价订单照片数据
-- ----------------------------
INSERT IGNORE INTO `www_yz_she_quote_photos` (`id`, `quote_order_id`, `photo_url`, `photo_type`, `is_defect`, `sort`, `create_time`) VALUES
(1, 2001, 'https://via.placeholder.com/300x300/007bff/ffffff?text=正面', 1, 0, 1, '2024-01-14 08:35:00'),
(2, 2001, 'https://via.placeholder.com/300x300/007bff/ffffff?text=背面', 1, 0, 2, '2024-01-14 08:35:00'),
(3, 2001, 'https://via.placeholder.com/300x300/007bff/ffffff?text=侧面', 1, 0, 3, '2024-01-14 08:35:00'),
(4, 2001, 'https://via.placeholder.com/300x300/dc3545/ffffff?text=划痕', 2, 1, 4, '2024-01-14 08:35:00'),
(5, 2002, 'https://via.placeholder.com/300x300/000000/ffffff?text=iPhone正面', 1, 0, 1, '2024-01-10 08:20:00'),
(6, 2002, 'https://via.placeholder.com/300x300/000000/ffffff?text=iPhone背面', 1, 0, 2, '2024-01-10 08:20:00'),
(7, 2003, 'https://via.placeholder.com/300x300/6c757d/ffffff?text=MacBook', 1, 0, 1, '2024-01-16 10:25:00');

-- ----------------------------
-- 9. 插入回收订单演示数据
-- ----------------------------
INSERT INTO `www_yz_she_recycle_orders` (
    `id`, `order_no`, `quote_order_id`, `member_id`, `category_id`, `brand_id`, `product_id`, 
    `product_name`, `product_code`, `product_image`, `status`, `source_type`, `quantity`,
    `expected_price`, `final_price`, `voucher_amount`, `total_amount`,
    `delivery_type`, `pickup_address_id`, `pickup_time`, `pickup_contact_name`, `pickup_contact_phone`, `pickup_address_detail`,
    `express_company`, `express_number`, `express_fee`,
    `quality_check_admin_id`, `quality_score`, `quality_note`, `quality_images`,
    `settlement_status`, `settlement_amount`, `settlement_admin_id`,
    `user_note`, `admin_note`,
    `pickup_time_actual`, `receive_time`, `quality_start_time`, `quality_complete_time`, `confirm_time`, `settlement_time`,
    `create_time`, `update_time`
) VALUES

-- 估价订单 - 待质检状态
(1, 'RC2024120001', 2001, 1001, 1, 1, NULL, 
'小米13 Ultra 512GB 黑色', NULL, NULL, 3, 1, 1,
3500.00, NULL, 200.00, NULL,
1, 1001, '2024-01-15 09:00-12:00', '张三', '13800138001', '北京市朝阳区建国路88号SOHO现代城A座1001室',
'SF', 'SF1234567890001', 0.00,
NULL, NULL, NULL, NULL,
0, NULL, NULL,
'手机成色较好，希望能给个好价格', '用户描述准确，设备状况良好',
UNIX_TIMESTAMP('2024-01-14 10:30:00'), UNIX_TIMESTAMP('2024-01-15 14:20:00'), NULL, NULL, NULL, NULL,
UNIX_TIMESTAMP('2024-01-14 08:33:44'), UNIX_TIMESTAMP('2024-01-15 14:20:00')),

-- 直接回收 - 待收货状态
(2, 'RC2024120002', NULL, 1002, 2, 2, 1001, 
'联想ThinkPad X1 Carbon 2023款', 'TPX1C2023', 'https://via.placeholder.com/200x200/28a745/ffffff?text=ThinkPad', 2, 2, 1,
8500.00, NULL, 0.00, NULL,
1, 1002, '2024-01-15 14:00-18:00', '李四', '13800138002', '上海市浦东新区陆家嘴环路1000号恒生银行大厦20楼',
'YTO', 'YT9876543210002', 0.00,
NULL, NULL, NULL, NULL,
0, NULL, NULL,
'笔记本电脑，使用一年，功能正常', '待收货确认',
UNIX_TIMESTAMP('2024-01-15 15:45:00'), NULL, NULL, NULL, NULL, NULL,
UNIX_TIMESTAMP('2024-01-15 10:22:15'), UNIX_TIMESTAMP('2024-01-15 15:45:00')),

-- 批量下单 - 待取件状态
(3, 'RC2024120003', NULL, 1003, 1, NULL, NULL, 
NULL, NULL, NULL, 1, 3, 5,
0.00, NULL, 0.00, NULL,
2, 1003, NULL, '王五', '13800138003', '广州市天河区珠江新城花城大道85号高德置地广场A座',
NULL, NULL, 0.00,
NULL, NULL, NULL, NULL,
0, NULL, NULL,
'有5台旧手机需要回收，具体型号现场确认', '批量回收订单，待现场确认',
NULL, NULL, NULL, NULL, NULL, NULL,
UNIX_TIMESTAMP('2024-01-15 16:45:30'), UNIX_TIMESTAMP('2024-01-15 16:45:30')),

-- 估价订单 - 已完成已结算
(4, 'RC2024120004', 2002, 1004, 1, 3, NULL, 
'iPhone 12 128GB 白色', NULL, NULL, 7, 1, 1,
3500.00, 3300.00, 100.00, 3400.00,
1, 1004, '2024-01-10 09:00-12:00', '赵六', '13800138004', '深圳市南山区科技园南区深南大道9988号',
'SF', 'SF1234567890004', 0.00,
1, 92, '外观完好，功能正常，电池健康度92%，无维修记录', '["https://via.placeholder.com/200x200?text=质检图1", "https://via.placeholder.com/200x200?text=质检图2"]',
1, 3400.00, 1,
'iPhone使用状况良好，无摔无修', '质检完成，用户满意',
UNIX_TIMESTAMP('2024-01-10 10:15:00'), UNIX_TIMESTAMP('2024-01-11 09:30:00'), UNIX_TIMESTAMP('2024-01-11 10:00:00'), UNIX_TIMESTAMP('2024-01-11 15:30:00'), UNIX_TIMESTAMP('2024-01-11 18:20:00'), UNIX_TIMESTAMP('2024-01-12 09:00:00'),
UNIX_TIMESTAMP('2024-01-10 08:15:22'), UNIX_TIMESTAMP('2024-01-12 09:00:00')),

-- 直接回收 - 待确认状态
(5, 'RC2024120005', NULL, 1005, 2, 4, 1002, 
'MacBook Pro 13英寸 M1', 'MBP13M1', 'https://via.placeholder.com/200x200/6c757d/ffffff?text=MacBook', 4, 2, 1,
9800.00, 9200.00, 0.00, NULL,
1, 1005, '2024-01-16 09:00-12:00', '钱七', '13800138005', '杭州市滨江区网商路699号阿里巴巴滨江园区',
'ZTO', 'ZT5678901234005', 0.00,
1, 88, '外观良好，屏幕有轻微划痕，电池循环次数较低', '["https://via.placeholder.com/200x200?text=MacBook质检1", "https://via.placeholder.com/200x200?text=MacBook质检2"]',
0, NULL, NULL,
'MacBook使用半年，基本无损', '质检完成，等待用户确认价格',
UNIX_TIMESTAMP('2024-01-16 10:30:00'), UNIX_TIMESTAMP('2024-01-17 08:45:00'), UNIX_TIMESTAMP('2024-01-17 09:00:00'), UNIX_TIMESTAMP('2024-01-17 16:30:00'), NULL, NULL,
UNIX_TIMESTAMP('2024-01-16 08:20:15'), UNIX_TIMESTAMP('2024-01-17 16:30:00'));

-- ----------------------------
-- 10. 插入回收订单状态变更日志
-- ----------------------------
INSERT INTO `www_yz_she_recycle_order_logs` (
    `recycle_order_id`, `from_status`, `to_status`, `operator_type`, `operator_id`, `operator_name`, 
    `operation_note`, `create_time`
) VALUES
-- 订单1的日志
(1, 0, 1, 2, 1001, '张三', '用户创建回收订单', UNIX_TIMESTAMP('2024-01-14 08:33:44')),
(1, 1, 2, 1, 1, '管理员', '快递已取件', UNIX_TIMESTAMP('2024-01-14 10:30:00')),
(1, 2, 3, 1, 1, '管理员', '仓库已收货', UNIX_TIMESTAMP('2024-01-15 14:20:00')),

-- 订单2的日志
(2, 0, 1, 2, 1002, '李四', '用户创建回收订单', UNIX_TIMESTAMP('2024-01-15 10:22:15')),
(2, 1, 2, 1, 1, '管理员', '快递已取件', UNIX_TIMESTAMP('2024-01-15 15:45:00')),

-- 订单3的日志
(3, 0, 1, 2, 1003, '王五', '用户创建批量回收订单', UNIX_TIMESTAMP('2024-01-15 16:45:30')),

-- 订单4的完整日志
(4, 0, 1, 2, 1004, '赵六', '用户创建回收订单', UNIX_TIMESTAMP('2024-01-10 08:15:22')),
(4, 1, 2, 1, 1, '管理员', '快递已取件', UNIX_TIMESTAMP('2024-01-10 10:15:00')),
(4, 2, 3, 1, 1, '管理员', '仓库已收货', UNIX_TIMESTAMP('2024-01-11 09:30:00')),
(4, 3, 4, 1, 1, '质检员', '质检完成，最终价格3300元', UNIX_TIMESTAMP('2024-01-11 15:30:00')),
(4, 4, 7, 2, 1004, '赵六', '用户确认价格', UNIX_TIMESTAMP('2024-01-11 18:20:00')),

-- 订单5的日志
(5, 0, 1, 2, 1005, '钱七', '用户创建回收订单', UNIX_TIMESTAMP('2024-01-16 08:20:15')),
(5, 1, 2, 1, 1, '管理员', '快递已取件', UNIX_TIMESTAMP('2024-01-16 10:30:00')),
(5, 2, 3, 1, 1, '管理员', '仓库已收货', UNIX_TIMESTAMP('2024-01-17 08:45:00')),
(5, 3, 4, 1, 1, '质检员', '质检完成，最终价格9200元', UNIX_TIMESTAMP('2024-01-17 16:30:00'));

SET FOREIGN_KEY_CHECKS = 1;
